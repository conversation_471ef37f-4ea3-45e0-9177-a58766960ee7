import axios from "../../utilities/Axios";

class NumbersService {
  async add(phone) {
    const data = {
      msisdn: phone,
    };

    return axios.post(`/linked-number/add/${phone}`, data, {
      headers: {
        "Content-Type": "application/json",
      },
    });
  }

  async remove(phone) {
    return axios.post(`/linked-number/remove/${phone}`, user, {
      headers: {
        "Content-type": "application/json",
        "User-Agent": user.agent,
      },
    });
  }

  async getNumbers(phone) {
    return axios.get(`/linked-numbers/${phone}`, {
      headers: {
        "Content-type": "application/json",
      },
    });
  }

  async verifyPhoneNumber(user) {
    return axios.post(`/auth/verify-phone-number`, user, {
      headers: {
        "Content-type": "application/json",
      },
    });
  }

  async VerifyOtp(number, otp) {
    return axios.get(`/auth/otp/verify/${number}/${otp}`, {
      headers: {
        "Content-type": "application/json",
      },
    });
  }
}

export default new NumbersService();
