import axios from "../../utilities/Axios";

class AirtimeBundleService {
  async GetBundles() {
    return axios.get(
      `/telco-mobile-erp/buy-usd-bundle/v1/get-usd-bundles-categories`,
      {
        headers: {
          "Content-type": "application/json",
        },
      }
    );
  }

  async buyBundle(data) {
    return axios.post(
      `/telco-mobile-erp/buy-usd-bundle/v1/purchase-bundle`,
      data,
      {
        headers: {
          "Content-type": "application/json",
        },
      }
    );
  }

  async rechargeAirtime(data) {
    return axios.post(
      `/telco-mobile-erp/one-seven-one/v1/prepaid-recharge-voucher-card`,
      data,
      {
        headers: {
          "Content-type": "application/json",
        },
      }
    );
  }

  async creditTransfer(data) {
    return axios.post(
      `/telco-mobile-erp/one-seven-one/v1/balance-transfer`,
      data,
      {
        headers: {
          "Content-type": "application/json",
        },
      }
    );
  }
}

export default new AirtimeBundleService();
