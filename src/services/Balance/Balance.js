import axios from "../../utilities/Axios";

class BalanceService {
  async getBalance(phone) {
    return axios.get(`/telco-mobile-erp/selfcare/v1/balance-enquiry/${phone}`, {
      headers: {
        "Content-type": "application/json",
      },
    });
  }

  async getBalancePostPaid(phone) {
    return axios.get(
      `/zsmart-core/mobile-erp/selfcare/postpaid/balance-enquiry/${phone}`,
      {
        headers: {
          "Content-type": "application/json",
        },
      }
    );
  }

  async getSubscriberProfile(phone) {
    return axios.get(
      `/telco-mobile-erp/buy-usd-bundle/v1/query-subscriber-profile/${phone}`,
      {
        headers: {
          "Content-type": "application/json",
        },
      }
    );
  }

  async getPostpaidBalance(phone) {
    return axios.get(
      `/zsmart-core/mobile-erp/selfcare/postpaid/balance-enquiry/${phone}`,
      {
        headers: {
          "Content-type": "application/json",
        },
      }
    );
  }
}

export default new BalanceService();
