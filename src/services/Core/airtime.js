import axios, { instance3 } from "../../utilities/Axios";

class Airtime {
  async buyBundle(data) {
    console.log(data);
    return instance3.post(`/core/buy-airtime`, data, {
      headers: {
        "Content-type": "application/json",
      },
    });
  }
  async buyBundleUSD(data) {
    return instance3.post(`/core/buy-usd-airtime`, data, {
      headers: {
        "Content-type": "application/json",
      },
    });
  }
}

export default new Airtime();
