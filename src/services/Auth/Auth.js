import axios, { instance3 } from "../../utilities/Axios";

class AuthService {
  async sendOtpForLogin(msisdn) {
    return axios.post(
      `/auth/v1/send-otp`,
      { msisdn },
      {
        headers: {
          "Content-type": "application/json",
        },
      }
    );
  }

  async verifyOtpForLogin(msisdn, otp) {
    return axios.post(
      `/auth/v1/verify-otp`,
      { msisdn, otp },
      {
        headers: {
          "Content-type": "application/json",
        },
      }
    );
  }

  async login(data) {
    console.log(data);
    return axios.post(`/auth/v1/signin`, data, {
      headers: {
        "Content-type": "application/json",
      },
    });
  }

  async register(user) {
    return axios.post(`/auth/v2/signup`, user, {
      headers: {
        "Content-type": "application/json",
        "User-Agent": user.agent,
      },
    });
  }

  async getProfile(phone) {
    return axios.get(`/${phone}/profile/data`, {
      headers: {
        "Content-type": "application/json",
      },
    });
  }

  async verifyPhoneNumber(user) {
    return axios.post(`/auth/verify-phone-number`, user, {
      headers: {
        "Content-type": "application/json",
      },
    });
  }

  async forgotPassword(data) {
    return axios.post(
      `/auth/set-password`,
      {
        password: data.password,
      },
      {
        headers: {
          "Content-type": "application/json",
          Authorization: "Bearer " + data.token,
          "x-token": data.token,
        },
      }
    );
  }

  async changePassword(data) {
    return axios.post(`/auth/change-password`, data, {
      headers: {
        "Content-type": "application/json",
      },
    });
  }

  async GenerateOtp(number) {
    return axios.get(`/auth/otp/generate/${number}`, {
      headers: {
        "Content-type": "application/json",
      },
    });
  }

  async VerifyOtp(number, otp) {
    return axios.get(`/auth/otp/verify/${number}/${otp}`, {
      headers: {
        "Content-type": "application/json",
      },
    });
  }

  async VerifyOtpForgotPassword(number, otp) {
    return axios.get(`/auth/otp/verify/subscriber/${number}/${otp}`, {
      headers: {
        "Content-type": "application/json",
      },
    });
  }

  async setDevice(data) {
    return axios.put(
      `/auth/change-device/${data.deviceId}`,
      {},
      {
        headers: {
          "Content-type": "application/json",
          "x-token": data.token,
          "User-Agent": data.agent,
          Authorization: "Bearer " + data.token,
        },
      }
    );
  }
  async customerLookup(data) {
    const number = "263" + data;
    return instance3.get(`/lookup/customer-details/${number}`, {
      headers: {
        "Content-type": "application/json",
      },
    });
  }
}

export default new AuthService();
