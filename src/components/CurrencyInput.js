import { Input, Text } from "native-base";
import React, { useState } from "react";
import { moderateScale, verticalScale } from "react-native-size-matters";

const CurrencyInputField = ({ decimalPlaces = 2, onChange, error }) => {
  const [value, setValue] = useState("");

  const handleChange = (text) => {
    const cleanedText = text.replace(/[^0-9.]/g, "");

    // Remove leading zeros
    const withoutLeadingZeros = cleanedText.replace(/^0+/, "");

    const decimalIndex = withoutLeadingZeros.indexOf(".");
    const hasDecimal = decimalIndex !== -1;

    if (hasDecimal) {
      const numDecimalPlaces = withoutLeadingZeros.length - decimalIndex - 1;
      if (numDecimalPlaces > decimalPlaces) {
        setValue(
          withoutLeadingZeros.slice(0, decimalIndex + decimalPlaces + 1)
        );
        return;
      }
    }

    setValue(withoutLeadingZeros);

    if (onChange) {
      onChange(withoutLeadingZeros);
    }
  };

  return (
    <>
      <Input
        keyboardType="numeric"
        value={value}
        onChangeText={handleChange}
        fontFamily={"openSansMedium"}
        focusOutlineColor={"#F68C1E"}
        borderColor={error ? "red.400" : "gray.300"}
        placeholder="Amount"
        fontSize={moderateScale(15)}
        bgColor={"gray.100"}
        rounded={"lg"}
        h={verticalScale(48)}
      />

      {error && (
        <Text color={"red.400"} fontSize={moderateScale(15)}>
          {error}
        </Text>
      )}
    </>
  );
};

export default CurrencyInputField;
