import { Feather } from "@expo/vector-icons"
import { HStack, Text, View } from "native-base"
import React from "react"
import { TouchableOpacity } from "react-native"
import { moderateScale } from "react-native-size-matters"

const AppbarCancel = ({ goBack, title }) => {
  return (
    <View>
      <HStack
        bg="white"
        py="3"
        justifyContent="space-between"
        alignItems="center"
        w="100%"
      >
        <HStack alignItems="center" w={"100%"} justifyContent={"space-between"}>
          <TouchableOpacity onPress={goBack}>
            <Feather name="x" size={24} color="black" />
          </TouchableOpacity>
          <View>
            <Text
              fontFamily={"openSansBold"}
              fontSize={moderateScale(16)}
              color={"#252622"}
              fontWeight="500"
            >
              {title}
            </Text>
          </View>
          <View></View>
        </HStack>
      </HStack>
    </View>
  )
}

export default AppbarCancel
