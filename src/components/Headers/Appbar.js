import { Ionicons } from "@expo/vector-icons"
import { HStack, View } from "native-base"
import React from "react"
import { TouchableOpacity } from "react-native"
import { scale } from "react-native-size-matters"

const Appbar = ({ goBack }) => {
  return (
    <View>
      <HStack
        bg="transparent"
        py="3"
        justifyContent="space-between"
        alignItems="center"
        w="100%"
      >
        <HStack alignItems="center">
          <TouchableOpacity onPress={goBack}>
            <Ionicons
              name="arrow-back-outline"
              size={scale(16)}
              color="black"
            />
          </TouchableOpacity>
        </HStack>
      </HStack>
    </View>
  )
}

export default Appbar
