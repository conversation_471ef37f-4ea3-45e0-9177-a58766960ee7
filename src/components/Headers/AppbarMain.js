import {
  AntDesign,
  MaterialCommunityIcons,
  FontAwesome,
  FontAwesome5,
} from "@expo/vector-icons";
import { Avatar, HStack, Image, Text, View } from "native-base";
import React from "react";
import { TouchableOpacity } from "react-native";
import { moderateScale, verticalScale, scale } from "react-native-size-matters";
import netone from "../../assets/Icons/netone.png";

const AppbarMain = ({ goBack, greeting, name, phone, logout, initials }) => {
  return (
    <View>
      <HStack
        bg="transparent"
        py={moderateScale(4)}
        justifyContent="space-between"
        alignItems="center"
        w="100%">
        <HStack
          bg={"transparent"}
          py={moderateScale(15)}
          justifyContent="space-between"
          alignItems="center"
          w="100%">
          <HStack space={moderateScale(10)} alignItems={"center"}>
            <View
              p={moderateScale(10)}
              w={moderateScale(50)}
              h={moderateScale(50)}
              justifyContent={"center"}
              alignItems={"center"}
              bg="white"
              rounded={"full"}>
              <FontAwesome5
                name="user-alt"
                size={moderateScale(20)}
                color="#949494"
              />
            </View>
            <View>
              <Text
                fontFamily={"openSansBold"}
                fontSize={moderateScale(15)}
                color={"white"}>
                Hello, {"263" + phone}
              </Text>
            </View>
          </HStack>

          <View
            p={moderateScale(1)}
            overflow={"hidden"}
            w={moderateScale(80)}
            h={moderateScale(50)}
            justifyContent={"center"}
            alignItems={"center"}
            bg="transparent"
            rounded={"xl"}>
            <Image
              source={netone}
              height={verticalScale(50)}
              width={scale(70)}
              alt={"OneMoney"}
            />
          </View>
        </HStack>
      </HStack>
    </View>
  );
};

export default AppbarMain;
