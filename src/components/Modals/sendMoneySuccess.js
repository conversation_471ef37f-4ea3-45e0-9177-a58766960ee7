import { useNavigation } from "@react-navigation/native";
import LottieView from "lottie-react-native";
import { Button, Modal, Text, VStack } from "native-base";
import React, { useRef, useState } from "react";
import { moderateScale, verticalScale } from "react-native-size-matters";

const SendMoneySuccess = () => {
  const [placement, setPlacement] = useState(undefined);
  const animation = useRef(null);
  const [open, setOpen] = useState(true);
  const navigation = useNavigation();

  const openModal = (placement) => {
    setOpen(true);
    setPlacement(placement);
  };

  return (
    <>
      <Modal useRNModal={true} isOpen={open} safeAreaTop={true} size={"full"}>
        <Modal.Content maxWidth="350" rounded={"xl"} bg={"white"}>
          <Modal.Body>
            <VStack alignItems={"center"} px={2} py={2}>
              <Text
                fontSize={moderateScale(20)}
                color={"gray.600"}
                fontFamily={"openSansBold"}>
                Purchase was successful
              </Text>
              <LottieView
                autoPlay
                ref={animation}
                style={{
                  width: 150,
                  height: 150,
                  backgroundColor: "white",

                  right: 40,
                }}
                // Find more Lottie files at https://lottiefiles.com/featured
                source={require("../../assets/lottie/success.json")}
              />
              <Button
                mt={moderateScale(30)}
                w={"100%"}
                onPress={() => {
                  navigation.navigate("Tabs");
                }}
                h={verticalScale(48)}
                bg="#F68C1E"
                rounded={"lg"}>
                <Text
                  fontSize={moderateScale(15)}
                  color="white"
                  fontFamily={"openSansSemiBold"}>
                  Done
                </Text>
              </Button>
            </VStack>
          </Modal.Body>
        </Modal.Content>
      </Modal>
    </>
  );
};

export default SendMoneySuccess;
