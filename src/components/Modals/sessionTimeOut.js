import { useNavigation } from "@react-navigation/native";
import LottieView from "lottie-react-native";
import { Button, Modal, Text, VStack } from "native-base";
import React, { useRef, useState } from "react";
import { moderateScale, verticalScale } from "react-native-size-matters";

const SessionTimeOut = () => {
  const [placement, setPlacement] = useState(undefined);
  const animation = useRef(null);
  const [open, setOpen] = useState(true);
  const navigation = useNavigation();

  const openModal = (placement) => {
    setOpen(true);
    setPlacement(placement);
  };

  return (
    <>
      <Modal useRNModal={true} isOpen={open} safeAreaTop={true} size={"xl"}>
        <Modal.Content maxWidth="350" rounded={"xl"} bg={"white"}>
          <Modal.Body>
            <VStack alignContent={"center"} alignItems={"center"} px={2} py={2}>
              <Text
                fontSize={moderateScale(20)}
                color={"gray.600"}
                fontFamily={"openSansBold"}>
                Your session has expired.
              </Text>
              <Text
                fontSize={moderateScale(16)}
                color={"gray.600"}
                fontFamily={"openSansBold"}>
                Please sign in again to continue.
              </Text>
              <LottieView
                autoPlay
                ref={animation}
                style={{
                  width: 200,
                  height: 200,
                  backgroundColor: "white",
                }}
                // Find more Lottie files at https://lottiefiles.com/featured
                source={require("../../assets/lottie/sessionTime.json")}
              />
              <Button
                mt={moderateScale(1)}
                w={"100%"}
                onPress={() => {
                  navigation.navigate("Login");
                }}
                h={verticalScale(48)}
                bg="#F68C1E"
                rounded={"lg"}>
                <Text
                  fontSize={moderateScale(15)}
                  color="white"
                  fontFamily={"openSansSemiBold"}>
                  Go to Sign In
                </Text>
              </Button>
            </VStack>
          </Modal.Body>
        </Modal.Content>
      </Modal>
    </>
  );
};

export default SessionTimeOut;
