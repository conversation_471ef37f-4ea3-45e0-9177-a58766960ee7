import { Center, Divider, Input, Modal, Text, View } from "native-base"
import React, { useEffect, useState } from "react"
import {
  Alert,
  PermissionsAndroid,
  Platform,
  StyleSheet,
  TouchableOpacity,
} from "react-native"
import Contacts from "react-native-contacts"
import Toast from "react-native-root-toast"
import { moderateScale, verticalScale } from "react-native-size-matters"

const GetContacts = (props) => {
  const [placement, setPlacement] = useState(undefined)
  const [open, setOpen] = useState(true)
  const [contacts, setContacts] = useState([])
  const [searchText, setSearchText] = useState("")
  const [loading, setLoading] = useState(false)
  const [failed, setFailed] = useState(false)

  useEffect(() => {
    const interval = setTimeout(() => {
      setFailed(false)
    }, 5000)

    return () => clearInterval(interval)
  }, [failed])

  useEffect(() => {
    const perm = async () => {
      if (Platform.OS === "android") {
        const checkPermission = await PermissionsAndroid.check(
          PermissionsAndroid.PERMISSIONS.READ_CONTACTS
        )

        if (checkPermission === false) {
          const granted = await PermissionsAndroid.request(
            PermissionsAndroid.PERMISSIONS.READ_CONTACTS,
            {
              title: "Access Contacts",
              message: "OneMoney wants to view your phone contacts.",
              buttonPositive: "OK",
            }
          )

          if (granted === true) {
            accessContacts()
          } else {
            setFailed(true)
            setErrorMessage("You don't have access for the location")
          }
        }
      } else {
        accessContacts()
      }
    }
    perm()
  }, [])

  const accessContacts = () => {
    setLoading(true)
    Contacts.getAll()
      .then((contacts) => {
        contacts.sort(
          (a, b) => a.givenName.toLowerCase() > b.givenName.toLowerCase()
        )
        setContacts(contacts)
        setLoading(false)
      })
      .catch((err) => {
        setLoading(false)
        if (err === "denied") {
          Alert.alert("Permission to access contacts was denied")
        }
      })
  }

  const filterData = (data) => {
    return data.filter((item) =>
      item.givenName.toLowerCase().includes(searchText.toLowerCase())
    )
  }

  const openModal = (placement) => {
    setOpen(true)
    setPlacement(placement)
  }

  return (
    <Modal
      useRNModal={true}
      isOpen={open}
      safeAreaTop={true}
      size={"full"}
      onClose={props.close}
    >
      <Toast
        visible={failed}
        position={30}
        shadow={true}
        animation={true}
        hideOnPress={true}
        backgroundColor={"red"}
        opacity={0.9}
      >
        {errorMessage}
      </Toast>
      <Modal.Content maxWidth="350" rounded={"xl"} bg={"white"}>
        <Modal.CloseButton />
        <Modal.Body>
          <View w="100%" h={"100%"} px={moderateScale(6)} pt={moderateScale(4)}>
            <Input
              fontFamily={"openSansSemiBold"}
              my={moderateScale(20)}
              value={searchText}
              focusOutlineColor={"#F68C1E"}
              borderColor={"gray.300"}
              placeholder="Search Contact"
              fontSize={moderateScale(15)}
              bgColor={"gray.100"}
              rounded={"lg"}
              onChangeText={(text) => setSearchText(text)}
              h={verticalScale(48)}
            />
            {loading && (
              <Center>
                <Text color={"blue.500"}>Loading ...</Text>
              </Center>
            )}

            {contacts ? (
              <View>
                {filterData(contacts).map((contact, index) => (
                  <View key={index}>
                    <View style={styles.MainContainer}>
                      <TouchableOpacity
                        onPress={() => {
                          props.setNumber(
                            contact.phoneNumbers?.[0].number
                              .toString()
                              .replace(/\s/g, "")
                              .replace("+263", "")
                          )
                          props.close()
                        }}
                      >
                        <View style={styles.row}>
                          <View style={styles.avatarContainer}></View>
                          <View style={styles.listTextContainer}>
                            <View style={{ justifyContent: "center" }}>
                              <Text style={{ fontSize: 18 }}>
                                {" "}
                                {`${contact.displayName}`}{" "}
                              </Text>
                            </View>
                          </View>
                        </View>
                      </TouchableOpacity>
                    </View>

                    <Divider />
                  </View>
                ))}
              </View>
            ) : (
              <View></View>
            )}
          </View>
        </Modal.Body>
      </Modal.Content>
    </Modal>
  )
}

const styles = StyleSheet.create({
  MainContainer: {
    flex: 1,
    paddingTop: Platform.OS === "ios" ? 20 : 0,
  },

  title: {
    padding: 12,
    fontSize: 22,
    backgroundColor: "#33691E",
    color: "white",
  },

  contactTitle: {
    fontSize: 22,
    paddingTop: 8,
    paddingBottom: 8,
    paddingLeft: 15,
    color: "black",
  },

  row: {
    flexDirection: "row",
    height: 60,
  },

  avatarContainer: {
    marginLeft: 12,
    justifyContent: "center",
    alignItems: "center",
  },

  listTextContainer: {
    marginLeft: 15,
    flexDirection: "row",
    flex: 1,
  },
})

export default GetContacts
