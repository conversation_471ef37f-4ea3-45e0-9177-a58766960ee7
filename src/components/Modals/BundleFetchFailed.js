import { useNavigation } from "@react-navigation/native";
import <PERSON><PERSON><PERSON>ie<PERSON> from "lottie-react-native";
import { Button, HStack, Modal, Text, VStack } from "native-base";
import React, { useRef, useState } from "react";
import { moderateScale, verticalScale } from "react-native-size-matters";
import { <PERSON><PERSON><PERSON>, Oct<PERSON><PERSON>, Feather } from "@expo/vector-icons";

const BundleFetchFailed = ({ fetchBundles }) => {
  const [placement, setPlacement] = useState(undefined);
  const animation = useRef(null);
  const [open, setOpen] = useState(true);
  const navigation = useNavigation();

  const openModal = (placement) => {
    setOpen(true);
    setPlacement(placement);
  };

  return (
    <>
      <Modal useRNModal={true} isOpen={open} safeAreaTop={true} size={"xl"}>
        <Modal.Content maxWidth="350" rounded={"xl"} bg={"white"}>
          <Modal.Body>
            <VStack
              alignContent={"center"}
              alignItems={"center"}
              space={moderateScale(10)}
              px={2}
              py={2}>
              <Text
                fontSize={moderateScale(17)}
                color={"gray.600"}
                fontFamily={"openSansBold"}>
                Failed To Fetch Bundles
              </Text>
              <Button
                mt={moderateScale(1)}
                w={"100%"}
                onPress={() => {
                  fetchBundles();
                }}
                h={verticalScale(48)}
                bg="gray.300"
                rounded={"lg"}>
                <HStack alignItems={"center"} space={moderateScale(10)}>
                  <Text
                    fontSize={moderateScale(15)}
                    color="black"
                    fontFamily={"openSansSemiBold"}>
                    Retry
                  </Text>
                  <Feather name="refresh-cw" size={moderateScale(16)} />
                </HStack>
              </Button>
              <Button
                mt={moderateScale(1)}
                w={"100%"}
                onPress={() => {
                  navigation.goBack();
                }}
                h={verticalScale(48)}
                bg="#F68C1E"
                rounded={"lg"}>
                <Text
                  fontSize={moderateScale(15)}
                  color="white"
                  fontFamily={"openSansSemiBold"}>
                  Go to Home Page
                </Text>
              </Button>
            </VStack>
          </Modal.Body>
        </Modal.Content>
      </Modal>
    </>
  );
};

export default BundleFetchFailed;
