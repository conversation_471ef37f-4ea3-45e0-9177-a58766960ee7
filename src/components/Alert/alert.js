import { Alert, HStack, Text, VStack, View } from "native-base"
import React from "react"

const Alerts = ({ status, show, error }) => {
  return (
    <View>
      <Alert
        w="100%"
        variant={"top-accent"}
        colorScheme={status}
        status={status}
      >
        <VStack space={2} flexShrink={1} w="100%">
          <HStack
            flexShrink={1}
            space={2}
            alignItems="center"
            justifyContent="space-between"
          >
            <HStack space={2} flexShrink={1} alignItems="center">
              <Alert.Icon />
              <Text>{error}</Text>
            </HStack>
          </HStack>
        </VStack>
      </Alert>
    </View>
  )
}

export default Alerts
