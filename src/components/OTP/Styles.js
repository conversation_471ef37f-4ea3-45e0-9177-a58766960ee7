import styled from "styled-components/native"

export const OTPInputContainer = styled.View`
  justify-content: center;
  align-items: center;
`

export const TextInputHidden = styled.TextInput`
  /* width: 300px;
border-color: #e5e5e5;
border-width: 1px;
border-radius: 5px;
padding: 15px;
margin-top: 50px;
color: white; */
  position: absolute;
  opacity: 0;
`
export const SplitOTPBoxesContainer = styled.Pressable`
  width: 100%;
  flex-direction: row;
  justify-content: space-around;
`
export const SplitBoxes = styled.View`
  border-color: #e5e5e5;
  border-width: 2px;
  background-color: #e5e5e5;
  border-radius: 10px;
  padding: 10px;
  min-height: 70px;
  min-width: 70px;
`

export const SplitBoxText = styled.Text`
  font-size: 35px;
  text-align: center;
  color: #f68c1e;
  fontfamily: "openSansMedium";
`

export const SplitBoxesFocused = styled(SplitBoxes)`
  border-color: #f68c1e;
  background-color: white;
`
