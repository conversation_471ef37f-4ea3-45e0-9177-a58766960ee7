import React, { createContext, useContext, useEffect, useState } from "react";
import { useSessionStore } from "./zustandStore";
import { isTokenExpired, getUserInfoFromToken } from "./tokenManager";
import LocalStore from "./store";

const AuthContext = createContext();

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [initialLoading, setInitialLoading] = useState(true);
  const [userInfo, setUserInfo] = useState(null);
  const {
    isAuthenticated,
    token,
    setSession,
    clearSession,
    checkSessionValidity,
    isSessionExpired,
  } = useSessionStore();

  useEffect(() => {
    const initializeAuth = async () => {
      try {
        // Check if we have a token in local storage
        const storedToken = await LocalStore.getData("@userToken");

        if (storedToken?.token) {
          // Check if token is expired
          if (isTokenExpired(storedToken.token)) {
            // Token is expired, clear session
            clearSession();
            await LocalStore.deleteData("@userToken");
          } else {
            // Check if session is still valid (24 hours)
            if (checkSessionValidity()) {
              // Session is still valid, set it
              setSession(storedToken.token);
              // Extract user info from token
              const userInfo = getUserInfoFromToken(storedToken.token);
              setUserInfo(userInfo);
            } else {
              // Session expired, clear it
              clearSession();
              await LocalStore.deleteData("@userToken");
            }
          }
        } else {
          // No token found, ensure session is cleared
          clearSession();
        }
      } catch (error) {
        console.error("Error initializing auth:", error);
        clearSession();
      } finally {
        setInitialLoading(false);
      }
    };

    initializeAuth();
  }, []);

  const login = async (token) => {
    try {
      // Store token in local storage
      await LocalStore.saveData("@userToken", { token });

      // Set session in store
      setSession(token);

      // Extract and store user info
      const userInfo = getUserInfoFromToken(token);
      setUserInfo(userInfo);

      return true;
    } catch (error) {
      console.error("Error during login:", error);
      return false;
    }
  };

  const logout = async () => {
    try {
      // Clear token from local storage
      await LocalStore.deleteData("@userToken");
      await LocalStore.deleteData("@username");

      // Clear session from store
      clearSession();

      // Clear user info
      setUserInfo(null);

      return true;
    } catch (error) {
      console.error("Error during logout:", error);
      return false;
    }
  };

  const value = {
    isAuthenticated,
    token,
    userInfo,
    login,
    logout,
    initialLoading,
    checkSessionValidity,
    isSessionExpired: isSessionExpired(),
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};
