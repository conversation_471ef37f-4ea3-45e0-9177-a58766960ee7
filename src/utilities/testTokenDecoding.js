// Test script to verify JWT token decoding
import { jwtDecode } from "jwt-decode";

const testToken =
  "eyJhbGciOiJIUzUxMiJ9.eyJmaXJzdE5hbWUiOiJQYW5hc2hlIEUuIiwibGFzdE5hbWUiOm51bGwsInN1YiI6IjI2MzcxMjk4MDE3MCIsImlkTnVtYmVyIjoiNDQtMTEzOTI3IFYgMDQiLCJleHAiOjE3NTMwMDIxNjYsImlhdCI6MTc1MjgyOTM2NiwianRpIjoiMjYzNzEyOTgwMTcwIn0.n8qdZs5ScZA9oYE47gOzv1Y0rX6WtGHZl_o9mwPblQj-F8s6EkG8vNxp2G6ftFbOb_xlkcxqs8i9Yt9d4S2n5Q";

export const testTokenDecoding = () => {
  try {
    const decoded = jwtDecode(testToken);
    console.log("Decoded token:", decoded);

    // Check expiration
    const currentTime = Date.now() / 1000;
    const isExpired = decoded.exp < currentTime;

    console.log("Token expires at:", new Date(decoded.exp * 1000));
    console.log("Current time:", new Date(currentTime * 1000));
    console.log("Is expired:", isExpired);

    // Extract user info
    console.log("User info:", {
      firstName: decoded.firstName,
      lastName: decoded.lastName,
      msisdn: decoded.sub,
      idNumber: decoded.idNumber,
      jti: decoded.jti,
    });

    return { success: true, decoded, isExpired };
  } catch (error) {
    console.error("Token decoding error:", error);
    return { success: false, error };
  }
};

// Uncomment to test
// testTokenDecoding();
