import AsyncStorage from "@react-native-async-storage/async-storage"

class LocalStore {
  async saveData(key, data) {
    try {
      const jsonValue = JSON.stringify(data)
      await AsyncStorage.setItem(`${key}`, jsonValue)
      return "success"
    } catch (e) {
      return "failed"
    }
  }

  async getData(key) {
    try {
      const jsonValue = await AsyncStorage.getItem(`${key}`)
      return jsonValue != null ? JSON.parse(jsonValue) : null
    } catch (e) {
      return "failed"
    }
  }

  async mergeData(key, data) {
    try {
      const jsonValue = JSON.stringify(data)
      await AsyncStorage.mergeItem(`${key}`, jsonValue)
      return "success"
    } catch (e) {
      return "failed"
    }
  }

  async deleteData(key) {
    try {
      await AsyncStorage.removeItem(`${key}`)
      return "success"
    } catch (e) {
      return "failed"
    }
  }
}

export default new LocalStore()
