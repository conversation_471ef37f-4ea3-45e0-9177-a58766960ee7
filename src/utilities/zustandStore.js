import { create } from "zustand";
import { persist, createJSONStorage } from "zustand/middleware";
import AsyncStorage from "@react-native-async-storage/async-storage";

export const useSessionStore = create(
  persist(
    (set, get) => ({
      timeout: false,
      sessionStartTime: null,
      token: null,
      isAuthenticated: false,

      setTime: () => set((state) => ({ timeout: state.timeout })),

      setSession: (token) => {
        const sessionStartTime = new Date().getTime();
        set({
          token,
          sessionStartTime,
          isAuthenticated: true,
          timeout: false,
        });
      },

      clearSession: () => {
        set({
          token: null,
          sessionStartTime: null,
          isAuthenticated: false,
          timeout: false,
        });
      },

      checkSessionValidity: () => {
        const { sessionStartTime } = get();
        if (!sessionStartTime) return false;

        const now = new Date().getTime();
        const sessionDuration = now - sessionStartTime;
        const twentyFourHours = 24 * 60 * 60 * 1000; // 24 hours in milliseconds

        return sessionDuration < twentyFourHours;
      },

      isSessionExpired: () => {
        const { sessionStartTime } = get();
        if (!sessionStartTime) return true;

        const now = new Date().getTime();
        const sessionDuration = now - sessionStartTime;
        const twentyFourHours = 24 * 60 * 60 * 1000;

        return sessionDuration >= twentyFourHours;
      },
    }),
    {
      name: "session-store",
      storage: createJSONStorage(() => AsyncStorage),
      partialize: (state) => ({
        token: state.token,
        sessionStartTime: state.sessionStartTime,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
);
