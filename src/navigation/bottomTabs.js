import {
  Entypo,
  FontAwesome,
  MaterialCommunityIcons,
  MaterialIcons,
} from "@expo/vector-icons";
import { createBottomTabNavigator } from "@react-navigation/bottom-tabs";
import { useNavigation } from "@react-navigation/native";
import { useEffect, useState } from "react";
import { Alert, Platform } from "react-native";
import { moderateVerticalScale } from "react-native-size-matters";
import LocalStore from "../utilities/store";
import Main4 from "../screens/Main";
import { navigationref, isreadyref, navigate } from "./rootNav";

import Account from "../screens/Account";
import SelfCare from "../screens/SelfCare";
import FAQ from "../screens/FAQ/faq";
import { useAuth } from "../utilities/AuthContext";

const Tab = createBottomTabNavigator();

function Tabs() {
  const navigation = useNavigation();
  const { logout } = useAuth();
  const [logoutClicked, setLogoutClicked] = useState(false);
  const [open, setOpen] = useState(false);

  useEffect(() => {
    return () => {
      isreadyref.current = false;
    };
  }, []);

  const handleLogout = async () => {
    setLogoutClicked(true);
    const success = await logout();

    // Clear additional data
    await LocalStore.deleteData("@bundles");

    if (success) {
      navigation.navigate("Landing");
    } else {
      console.log("logout failed");
      setLogoutClicked(false);
    }
  };

  return (
    <>
      <Tab.Navigator
        onready={() => {
          isreadyref.current = true;
        }}
        initialRouteName="Main"
        screenOptions={{
          tabBarShowLabel: true,
          tabBarLabelStyle: {
            fontSize: moderateVerticalScale(12),
            fontFamily: "openSansSemiBold",
            marginBottom: 4,
          },
          tabBarStyle: {
            backgroundColor: "white",
            height:
              Platform.OS === "android"
                ? moderateVerticalScale(65)
                : moderateVerticalScale(85),
            paddingTop: 8,
            paddingBottom: Platform.OS === "android" ? 8 : 20,
            borderTopWidth: 1,
            borderTopColor: "#E5E7EB",
            elevation: 8,
            shadowColor: "#000",
            shadowOffset: {
              width: 0,
              height: -2,
            },
            shadowOpacity: 0.1,
            shadowRadius: 8,
          },
          tabBarIconStyle: {
            marginBottom: 2,
          },
        }}>
        <Tab.Screen
          listeners={{
            beforeRemove: (e) => {
              if (logoutClicked === false) {
                e.preventDefault();
                Alert.alert(
                  "Logging Out",
                  "Do you want to logout ?",
                  [
                    {
                      text: "No",
                      style: "cancel",
                    },
                    { text: "Yes", onPress: () => handleLogout() },
                  ],
                  { cancelable: false }
                );
              }
            },
          }}
          options={{
            headerShown: false,
            tabBarLabel: "Home",
            tabBarIcon: ({ color, size }) => (
              <MaterialCommunityIcons name="home" color={color} size={size} />
            ),
            tabBarActiveTintColor: "#F68C1E",
            tabBarInactiveTintColor: "#737373",
          }}
          name="Main"
          component={Main4}
          initialParams={{ current: "Main" }}
        />

        <Tab.Screen
          options={{
            headerShown: false,
            tabBarLabel: "Self Care",
            tabBarIcon: ({ color, size }) => (
              <MaterialCommunityIcons
                name="account-cog"
                color={color}
                size={size}
              />
            ),
            tabBarActiveTintColor: "#F68C1E",
            tabBarInactiveTintColor: "#737373",
          }}
          name="SelfCare"
          component={SelfCare}
        />

        <Tab.Screen
          options={{
            headerShown: false,
            tabBarLabel: "FAQ",
            tabBarIcon: ({ color, size }) => (
              <MaterialCommunityIcons
                name="help-circle"
                color={color}
                size={size}
              />
            ),
            tabBarActiveTintColor: "#F68C1E",
            tabBarInactiveTintColor: "#737373",
          }}
          name="FAQ"
          component={FAQ}
        />

        <Tab.Screen
          options={{
            headerShown: false,
            tabBarLabel: "Account",
            tabBarIcon: ({ color, size }) => (
              <MaterialCommunityIcons
                name="account"
                color={color}
                size={size}
              />
            ),
            tabBarActiveTintColor: "#F68C1E",
            tabBarInactiveTintColor: "#737373",
          }}
          name="Account"
          component={Account}
        />
      </Tab.Navigator>
    </>
  );
}

export default Tabs;
