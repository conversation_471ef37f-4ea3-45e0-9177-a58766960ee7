import { useNavigation } from "@react-navigation/native";
import { MaterialCommunityIcons, Entypo } from "@expo/vector-icons";
import {
  Box,
  Button,
  FormControl,
  HStack,
  Input,
  InputGroup,
  Text,
  VStack,
  View,
  Center,
  FlatList,
  Spinner,
} from "native-base";
import React, { useEffect, useRef, useState } from "react";
import { Dimensions, StyleSheet } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { moderateScale, verticalScale } from "react-native-size-matters";
import ZecService from "../../services/Zec/ZecService";
import LoadingModal from "../../components/Loading/LoadingModal";
import ActionSheet from "react-native-actions-sheet";
import Toast from "react-native-root-toast";

const Zec = ({ route }) => {
  const [errorMessage, setErrorMessage] = useState("");
  const [loading, setLoading] = useState(false);
  const height = Dimensions.get("window").height;
  const [failed, setFailed] = useState(false);
  const [success, setSuccess] = useState(false);
  const [successMessage, setSuccessMessage] = useState("");
  const navigation = useNavigation();
  const [nationalId, setNationalID] = useState(null);
  const [nationalIdError, setNationalIDError] = useState("");
  const [idField, setIdField] = useState(false);
  const [lastName, setLastName] = useState();
  const [errorLastName, setErrorLastName] = useState();
  const [details, setDetails] = useState([]);
  const actionSheetRef = useRef(null);
  const [loadingSend, setLoadingSend] = useState(false);

  const validate = () => {
    if (!lastName) {
      setErrorLastName("This field is required");
      return false;
    }
    if (!/^[a-zA-Z]+$/.test(lastName)) {
      setErrorLastName("Last name must contain letters only");
      return false;
    }
    //check national ID
    if (!nationalId) {
      setNationalIDError("This field is required");
      return false;
    }

    if (!idField) {
      setNationalIDError(
        "National ID field is invalid, check for space/s between letters or and the end or make sure its a correct zimbabwean ID."
      );
      return false;
    }

    return true;
  };

  useEffect(() => {
    const interval = setTimeout(() => {
      setFailed(false);
    }, 5000);
    return () => clearInterval(interval);
  }, [failed]);

  useEffect(() => {
    const interval = setTimeout(() => {
      setSuccess(false);
    }, 5000);
    return () => clearInterval(interval);
  }, [success]);

  const getDetails = async () => {
    try {
      const res = validate();

      if (!res) {
        return;
      }
      setLoading(true);
      setFailed(false);
      const response = await ZecService.getDetails(
        nationalId.toLocaleUpperCase(),
        lastName
      );
      console.log(response.data);

      if (response.data.responseCode === "200") {
        setLoading(false);
        setDetails(response.data.data);
        setTimeout(() => {
          actionSheetRef.current.show();
        }, 500);
      } else if (response.data.responseCode === "204") {
        setFailed(true);
        setErrorMessage("Details Not Found");
      } else {
        setFailed(true);
        setErrorMessage(response.data.message);
      }
      setLoading(false);
    } catch (error) {
      setLoading(false);
      setFailed(true);
      if (error.response) {
        if (error.response.status == 400) {
          setErrorMessage("Something went wrong, Please try again");
        } else if (
          error.response.status == 403 ||
          error.response.status == 500 ||
          error.response.status == 504
        ) {
          setErrorMessage("Server Error, Please try again later");
        } else {
          if (error.response.data.message) {
            setErrorMessage(error.response.data.message);
          } else {
            setErrorMessage("Something went wrong, Please try again");
          }
        }
      } else if (error.request) {
        setErrorMessage("Network Error, Please check your internet connection");
      } else {
        setErrorMessage("Something went wrong, Please try again");
      }
    }
  };

  const handleInputChange = (text) => {
    const regex = /^([0-9][0-9])-([0-9]{6}|[0-9]{7})-([a-zA-Z])([0-9]{2})$/;
    setIdField(regex.test(text));
    setNationalID(text);
  };

  return (
    <SafeAreaView>
      <View w="100%" bg={"white"} h={height}>
        <VStack flex={1} px={moderateScale(20)} w="100%">
          <VStack space={moderateScale(5)} mt={moderateScale(30)}>
            <Toast
              visible={failed}
              position={30}
              shadow={true}
              animation={true}
              hideOnPress={true}
              backgroundColor={"red"}
              opacity={0.9}>
              {errorMessage}
            </Toast>
            <Toast
              visible={success}
              position={30}
              shadow={true}
              animation={true}
              hideOnPress={true}
              backgroundColor={"green"}
              opacity={0.9}>
              {successMessage}
            </Toast>
            <LoadingModal isLoading={loading} />
            <FormControl>
              <Input
                fontFamily={"openSansSemiBold"}
                onFocus={() => {
                  setErrorLastName("");
                }}
                focusOutlineColor={"#F68C1E"}
                borderColor={errorLastName ? "red.400" : "gray.300"}
                placeholder="Surname"
                fontSize={moderateScale(15)}
                bgColor={"gray.100"}
                rounded={"lg"}
                onChangeText={(text) => setLastName(text)}
                h={verticalScale(48)}
              />
              <Text color={"red.400"} fontSize={moderateScale(15)}>
                {errorLastName}
              </Text>
            </FormControl>
            <FormControl>
              <Text
                fontFamily={"openSans"}
                color={"blue.600"}
                fontSize={moderateScale(15)}>
                Use this format: 00-000000-B00
              </Text>
              <Input
                fontFamily={"openSansSemiBold"}
                onFocus={() => {
                  setNationalIDError("");
                }}
                focusOutlineColor={"#F68C1E"}
                borderColor={nationalIdError ? "red.400" : "gray.300"}
                placeholder="National ID Number"
                fontSize={moderateScale(15)}
                bgColor={"gray.100"}
                rounded={"lg"}
                onChangeText={handleInputChange}
                h={verticalScale(48)}
              />
              <Text color={"red.400"} fontSize={moderateScale(15)}>
                {nationalIdError}
              </Text>
            </FormControl>
            <Button
              onPress={() => {
                getDetails();
              }}
              h={verticalScale(48)}
              bg="#F68C1E"
              rounded={"lg"}>
              <HStack space={moderateScale(10)} alignItems={"center"}>
                <Text
                  fontSize={moderateScale(15)}
                  color="white"
                  fontFamily={"openSansSemiBold"}>
                  Get Details
                </Text>
              </HStack>
            </Button>
          </VStack>
        </VStack>
        <ActionSheet
          ref={actionSheetRef}
          animated={true}
          gestureEnabled={loadingSend == true ? false : true}
          closeOnTouchBackdrop={loadingSend == true ? false : true}
          isModal={true}>
          {loading ? (
            <Spinner size={"lg"}></Spinner>
          ) : (
            <View
              w="100%"
              h={"80%"}
              px={moderateScale(16)}
              pt={moderateScale(4)}>
              <Center mb={moderateScale(30)}>
                <Text
                  fontFamily={"openSansSemiBold"}
                  mt={moderateScale(20)}
                  fontSize={moderateScale(15)}>
                  Registration Details
                </Text>
              </Center>
              <View h={"100%"}>
                <FlatList
                  flex={2}
                  showsVerticalScrollIndicator={false}
                  data={details}
                  renderItem={({ item, index }) => {
                    return (
                      <HStack
                        key={item.address}
                        backgroundColor={"gray.100"}
                        rounded={"xl"}
                        w="100%"
                        p={moderateScale(10)}
                        px={moderateScale(20)}
                        justifyContent={"space-between"}>
                        <HStack space={moderateScale(15)}>
                          <VStack space={moderateScale(10)}>
                            <Text
                              fontFamily={"openSansMedium"}
                              fontSize={moderateScale(13)}
                              color={"gray.800"}>
                              Forename : {item.forename}
                            </Text>

                            <Text
                              fontFamily={"openSansMedium"}
                              fontSize={moderateScale(13)}
                              color={"gray.800"}>
                              Surname : {item.surname}
                            </Text>
                            <Text
                              fontFamily={"openSansMedium"}
                              fontSize={moderateScale(13)}
                              color={"gray.800"}>
                              national ID : {item.id}
                            </Text>
                            <Text
                              fontFamily={"openSansMedium"}
                              fontSize={moderateScale(13)}
                              color={"gray.800"}>
                              Gender : {item.sex}
                            </Text>
                            <Text
                              fontFamily={"openSansMedium"}
                              fontSize={moderateScale(13)}
                              color={"gray.800"}>
                              DOB : {item.dob}
                            </Text>
                            <Text
                              fontFamily={"openSansMedium"}
                              fontSize={moderateScale(13)}
                              color={"gray.800"}>
                              Address : {item.address}
                            </Text>
                            <Text
                              fontFamily={"openSansMedium"}
                              fontSize={moderateScale(13)}
                              color={"gray.800"}>
                              Constituency : {item.constituency}
                            </Text>
                            <Text
                              fontFamily={"openSansMedium"}
                              fontSize={moderateScale(13)}
                              color={"gray.800"}>
                              District : {item.district}
                            </Text>

                            <Text
                              fontFamily={"openSansMedium"}
                              fontSize={moderateScale(13)}
                              color={"gray.800"}>
                              Local Authority : {item.localauthority}
                            </Text>
                            <Text
                              fontFamily={"openSansMedium"}
                              fontSize={moderateScale(13)}
                              color={"gray.800"}>
                              Polling Station : {item.pollingstation}
                            </Text>
                            <Text
                              fontFamily={"openSansMedium"}
                              fontSize={moderateScale(13)}
                              color={"gray.800"}>
                              registrationnumber : {item.registrationnumber}
                            </Text>

                            <Text
                              fontFamily={"openSansMedium"}
                              fontSize={moderateScale(13)}
                              color={"gray.800"}>
                              Station Code : {item.stationcode}
                            </Text>

                            <Text
                              fontFamily={"openSansMedium"}
                              fontSize={moderateScale(13)}
                              color={"gray.800"}>
                              Ward : {item.ward}
                            </Text>
                          </VStack>
                        </HStack>
                      </HStack>
                    );
                  }}
                  numColumns={1}
                  keyExtractor={(item) => item.accountResName}
                  ItemSeparatorComponent={() => {
                    return <Box m={moderateScale(10)}></Box>;
                  }}
                  ListEmptyComponent={() => {
                    return (
                      <Center>
                        <Text
                          fontFamily={"openSansSemiBold"}
                          mt={moderateScale(100)}
                          fontSize={moderateScale(15)}>
                          No Zec Details Found
                        </Text>
                      </Center>
                    );
                  }}
                />
              </View>
            </View>
          )}
        </ActionSheet>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: "center",
    padding: 20,
  },
  label: {
    fontSize: 16,
    marginTop: 20,
  },
  input: {
    width: "100%",
    height: 40,
    borderColor: "gray",
    borderWidth: 1,
    marginTop: 10,
    padding: 10,
  },
  button: {
    marginTop: 20,
    backgroundColor: "blue",
    padding: 10,
  },
  buttonText: {
    color: "white",
    fontSize: 16,
  },
  errorMessage: {
    marginTop: 20,
    color: "red",
    fontSize: 16,
  },
});

export default Zec;
