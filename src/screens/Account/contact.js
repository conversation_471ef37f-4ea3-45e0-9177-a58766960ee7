import {
  MaterialCommunityIcons,
  FontAwesome,
  FontAwesome5,
  FontAwesome6,
} from "@expo/vector-icons";
import { useNavigation } from "@react-navigation/native";
import {
  Box,
  HStack,
  Link,
  ScrollView,
  StatusBar,
  Text,
  VStack,
} from "native-base";
import React, { useEffect, useState } from "react";
import { Linking, SafeAreaView, TouchableOpacity } from "react-native";
import { moderateScale } from "react-native-size-matters";
import LocalStore from "../../utilities/store";

const Contact = ({ route }) => {
  const navigation = useNavigation();
  const [profile, setProfile] = useState(null);

  useEffect(() => {
    const getProfile = async () => {
      const profile = await LocalStore.getData("@userProfile");
      if (profile !== null) {
        setProfile(profile);
      }
    };
    getProfile();
  }, []);

  const contactInfo = [
    {
      icon: "phone",
      title: "Call Us",
      items: [
        { label: "Toll Free: 123", action: () => Linking.openURL("tel:123") },
        {
          label: "+263 712 980 795",
          action: () => Linking.openURL("tel:+263712980795"),
        },
        {
          label: "+263 712 980 898",
          action: () => Linking.openURL("tel:+263712980898"),
        },
        {
          label: "+263 712 980 880",
          action: () => Linking.openURL("tel:+263712980880"),
        },
      ],
      bgColor: "#10B981",
    },
    {
      icon: "map-marker",
      title: "HQ Address",
      items: [
        { label: "Floor Kopje building Harare" },
        { label: "1 Jason Moyo Avenue" },
        { label: "P.O BOx CY 579 Causeway" },
        { label: "Harare Zimbabwe" },
      ],
      bgColor: "#3B82F6",
    },
    {
      icon: "email",
      title: "Email Us",
      items: [
        {
          label: "<EMAIL>",
          action: () => Linking.openURL("mailto:<EMAIL>"),
        },
      ],
      bgColor: "#F59E0B",
    },
  ];

  const socialLinks = [
    {
      name: "facebook",
      url: "https://www.facebook.com/netonezimbabwe",
      color: "#1877F2",
      icon: FontAwesome,
    },
    {
      name: "tiktok",
      url: "https://www.tiktok.com/@NetOnecellular?lang=en",
      color: "#FF0050",
      icon: FontAwesome5,
    },
    {
      name: "instagram",
      url: "https://www.instagram.com/netonecellular/?hl=en",
      color: "#E4405F",
      icon: FontAwesome,
    },
    {
      name: "linkedin",
      url: "https://www.linkedin.com/company/NetOne-cellular",
      color: "#0A66C2",
      icon: FontAwesome,
    },
    {
      name: "x-twitter",
      url: "https://x.com/NetOneCellular",
      color: "#000000",
      icon: FontAwesome6,
    },
  ];

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: "white" }}>
      <StatusBar backgroundColor={"white"} barStyle="dark-content" />
      <ScrollView flex={1} bg={"gray.50"} showsVerticalScrollIndicator={false}>
        {/* Header */}
        <Box bg={"white"} px={moderateScale(20)} pt={moderateScale(10)}>
          <VStack space={moderateScale(16)} py={moderateScale(16)}>
            <Text
              fontFamily={"openSansBold"}
              fontSize={moderateScale(28)}
              color={"gray.900"}>
              Contact Us
            </Text>
            <Text
              fontFamily={"openSansMedium"}
              fontSize={moderateScale(16)}
              color={"gray.500"}>
              If you have any inquiries, get in touch with us. We are happy to
              help!
            </Text>
          </VStack>
        </Box>

        {/* Contact Information */}
        <Box px={moderateScale(20)} py={moderateScale(20)}>
          <VStack space={moderateScale(16)}>
            {contactInfo.map((section, index) => (
              <Box
                key={index}
                bg={"white"}
                rounded={"2xl"}
                p={moderateScale(20)}
                shadow={2}
                borderWidth={1}
                borderColor={"gray.100"}>
                <VStack space={moderateScale(16)}>
                  <HStack space={moderateScale(16)} alignItems={"center"}>
                    <Box
                      bg={section.bgColor}
                      rounded={"full"}
                      w={moderateScale(48)}
                      h={moderateScale(48)}
                      alignItems={"center"}
                      justifyContent={"center"}>
                      <MaterialCommunityIcons
                        name={section.icon}
                        size={moderateScale(24)}
                        color="white"
                      />
                    </Box>
                    <Text
                      fontFamily={"openSansBold"}
                      fontSize={moderateScale(18)}
                      color={"gray.900"}>
                      {section.title}
                    </Text>
                  </HStack>

                  <VStack space={moderateScale(8)}>
                    {section.items.map((item, itemIndex) => (
                      <TouchableOpacity
                        key={itemIndex}
                        onPress={item.action}
                        disabled={!item.action}>
                        <Text
                          fontFamily={"openSansMedium"}
                          fontSize={moderateScale(14)}
                          color={item.action ? "#3B82F6" : "gray.600"}
                          lineHeight={moderateScale(20)}>
                          {item.label}
                        </Text>
                      </TouchableOpacity>
                    ))}
                  </VStack>
                </VStack>
              </Box>
            ))}

            {/* Social Media Section */}
            <Box
              bg={"white"}
              rounded={"2xl"}
              p={moderateScale(20)}
              shadow={2}
              borderWidth={1}
              borderColor={"gray.100"}>
              <VStack space={moderateScale(16)}>
                <HStack space={moderateScale(16)} alignItems={"center"}>
                  <Box
                    bg={"#8B5CF6"}
                    rounded={"full"}
                    w={moderateScale(48)}
                    h={moderateScale(48)}
                    alignItems={"center"}
                    justifyContent={"center"}>
                    <MaterialCommunityIcons
                      name="heart"
                      size={moderateScale(24)}
                      color="white"
                    />
                  </Box>
                  <Text
                    fontFamily={"openSansBold"}
                    fontSize={moderateScale(18)}
                    color={"gray.900"}>
                    Follow Us
                  </Text>
                </HStack>

                <HStack space={moderateScale(12)} flexWrap={"wrap"}>
                  {socialLinks.map((social, index) => {
                    const IconComponent = social.icon;
                    return (
                      <Link
                        key={index}
                        href={social.url}
                        _pressed={{ opacity: 0.7 }}>
                        <Box
                          alignItems={"center"}
                          justifyContent={"center"}
                          w={moderateScale(48)}
                          h={moderateScale(48)}
                          bg={social.color}
                          rounded={"full"}
                          shadow={2}>
                          <IconComponent
                            name={social.name}
                            size={moderateScale(20)}
                            color="white"
                          />
                        </Box>
                      </Link>
                    );
                  })}
                </HStack>
              </VStack>
            </Box>
          </VStack>
        </Box>
      </ScrollView>
    </SafeAreaView>
  );
};

export default Contact;
