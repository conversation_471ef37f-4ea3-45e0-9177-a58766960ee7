import { FontAwesome5 } from "@expo/vector-icons";
import { useNavigation } from "@react-navigation/native";
import {
  Box,
  Button,
  Center,
  HStack,
  Heading,
  Text,
  VStack,
  View,
} from "native-base";
import React, { useEffect, useRef, useState } from "react";
import { Dimensions, Platform, SafeAreaView } from "react-native";
import Toast from "react-native-root-toast";
import { moderateScale, verticalScale } from "react-native-size-matters";
import LoadingModal from "../../components/Loading/LoadingModal";
import SendMoneySuccess from "../../components/Modals/sendMoneySuccess";
import airtimeBundles from "../../services/Bundles/Bundles";

const ConfirmCreditTransfer = ({ route }) => {
  const [loading, setLoading] = useState(false);
  const [failed, setFailed] = useState(false);
  const [dataDetails, setDataDetails] = useState(route.params.data);
  const [errorMessage, setErrorMessage] = useState("");
  const [success, setSuccess] = useState(false);
  const navigation = useNavigation();

  useEffect(() => {
    const interval = setTimeout(() => {
      setFailed(false);
    }, 5000);
    return () => clearInterval(interval);
  }, [failed]);

  const handleBuyBundle = async () => {
    setLoading(true);
    setFailed(false);
    try {
      const data = {
        senderPhoneNumber: dataDetails.buyerPhoneNumber,
        receiverPhoneNumber: dataDetails.receiverPhoneNumber,
        amount: dataDetails.amount,
      };

      const response = await airtimeBundles.creditTransfer(data);

      if (response.data.success) {
        navigation.navigate("SuccessSend", {
          data: {
            title: "Credit Transfer",
            message: "Transfer was successful",
            amount: dataDetails.amount,
            currency: dataDetails.amount,
            beneficiaryPhoneNumber: dataDetails.receiverPhoneNumber,
            status: "individual",
          },
        });
      } else {
        setFailed(true);
        setErrorMessage(response.data.message);
      }
      setLoading(false);
    } catch (error) {
      setLoading(false);
      setFailed(true);
      console.log(error);
      if (error.response) {
        if (error.response.status == 400) {
          setErrorMessage("Something went wrong, Please try again");
        } else if (
          error.response.status == 403 ||
          error.response.status == 500 ||
          error.response.status == 504
        ) {
          setErrorMessage("Server Error, Please try again later");
        } else {
          if (error.response.data.message) {
            setErrorMessage(error.response.data.message);
          } else {
            setErrorMessage("Something went wrong, Please try again");
          }
        }
      } else if (error.request) {
        setErrorMessage("Network Error, Please check your internet connection");
      } else {
        setErrorMessage("Something went wrong, Please try again");
      }
    }
  };
  return (
    <SafeAreaView>
      <View
        bg={"white"}
        style={{
          display: "flex",
          flexDirection: "column",
          justifyContent: "space-between",
          height: "100%",
        }}>
        <View px={moderateScale(20)} py={moderateScale(10)}>
          <LoadingModal isLoading={loading} />
          <Toast
            visible={failed}
            position={70}
            shadow={true}
            animation={true}
            hideOnPress={true}
            backgroundColor={"red"}
            opacity={0.9}
            duration={Toast.durations.LONG}>
            {errorMessage}
          </Toast>
          {success && <SendMoneySuccess></SendMoneySuccess>}
          <Center>
            <HStack
              mb={5}
              alignItems={"center"}
              space={moderateScale(6)}
              mt={moderateScale(50)}>
              <Text
                fontFamily={"openSansBold"}
                fontSize={moderateScale(15)}
                color={"#252622"}
                fontWeight="500">
                Transfering To:
              </Text>
            </HStack>
            <VStack justifyContent={"center"} alignItems={"center"}>
              <Heading
                fontFamily={"openSans"}
                mt={moderateScale(6)}
                fontSize={moderateScale(15)}
                color={"#252622"}
                fontWeight="500">
                {dataDetails.receiverPhoneNumber}
              </Heading>

              <Heading
                fontFamily={"openSans"}
                mt={moderateScale(6)}
                fontSize={moderateScale(15)}
                color={"#252622"}
                fontWeight="500">
                Amount: {dataDetails.currency} {dataDetails.amount}
              </Heading>
            </VStack>
          </Center>
        </View>
        <View px={moderateScale(20)} pb={moderateScale(50)}>
          <Button
            onPressIn={() => {}}
            mt={moderateScale(24)}
            onPress={() => handleBuyBundle()}
            h={verticalScale(48)}
            bg="#F68C1E"
            rounded={"lg"}>
            <Text
              fontSize={moderateScale(15)}
              color="white"
              fontFamily={"openSansSemiBold"}>
              Purchase
            </Text>
          </Button>
        </View>
      </View>
    </SafeAreaView>
  );
};

export default ConfirmCreditTransfer;
