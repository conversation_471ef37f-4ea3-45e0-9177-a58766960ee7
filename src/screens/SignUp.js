import { Feather } from "@expo/vector-icons";
import { useNavigation } from "@react-navigation/native";
import {
  Box,
  Button,
  Center,
  FormControl,
  Heading,
  Image,
  Input,
  InputGroup,
  InputRightAddon,
  InputLeftAddon,
  ScrollView,
  Text,
  VStack,
  View,
  useToast,
  TextArea,
  Tooltip,
  IconButton,
} from "native-base";
import React, { useEffect, useState } from "react";
import Toast from "react-native-root-toast";
import { moderateScale, scale, verticalScale } from "react-native-size-matters";
import netone from "../assets/Icons/netone2.png";
import LoadingModal from "../components/Loading/LoadingModal";
import AccountCreated from "../components/Modals/accountCreated";
import AuthService from "../services/Auth/Auth";
import LocalStore from "../utilities/store";
import CustomTooltip from "../components/Tooltip/tooltip";
import { TouchableOpacity } from "react-native";
import { getUniqueId } from "react-native-device-info";
import UserAgent from "react-native-user-agent";
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";

const SignUp = ({ route }) => {
  const navigation = useNavigation();
  const [email, setEmail] = useState("");
  const [emailError, setEmailError] = useState("");
  const [nationalId, setNationalID] = useState(null);
  const [nationalIdError, setNationalIDError] = useState("");
  const [password, setPassword] = useState("");
  const [passwordError, setPasswordError] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [confirmPasswordError, setConfirmPasswordError] = useState("");
  const [idField, setIdField] = useState(false);
  const [emailField, setEmailField] = useState(false);
  const [loading, setLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const toast = useToast();
  const [failed, setFailed] = useState(false);
  const [success, setSuccess] = useState(false);
  const [showpassword, setShowPassword] = useState(true);
  const [showconfirm, setShowConfirm] = useState(true);

  useEffect(() => {
    const interval = setTimeout(() => {
      setFailed(false);
    }, 5000);

    return () => clearInterval(interval);
  }, [failed]);

  const validate = () => {
    const numberRegexp = /^(071|71)/;
    //check email
    if (email && !emailField) {
      setEmailError("Invalid Email");
      return false;
    }

    if (!nationalId) {
      setNationalIDError("This field is required");
      return false;
    }

    if (!idField) {
      setNationalIDError(
        "National ID field is invalid, check for space/s between letters or and the end or make sure its a correct zimbabwean ID."
      );
      return false;
    }

    /*  if (!phone) {
      setPhoneError("This field is required");
      return false;
    }

    if (phone.length < 9) {
      setPhoneError("Invalid phone number, not enough digits");
      return false;
    }

    if (phone.length > 10) {
      setPhoneError("Invalid phone number, too many digits");
      return false;
    } */

    /*    if (!numberRegexp.test(phone)) {
      setPhoneError("Invalid phone number, use a netone number");
      return false;
    } */
    //check password
    if (!password) {
      setPasswordError("This field is required");
      return false;
    }

    if (password.length < 4) {
      setPasswordError("This field must not be less than 4 characters");
      return false;
    }

    //check confirm password
    if (!confirmPassword) {
      setConfirmPasswordError("This field is required");
      return false;
    }

    if (password !== confirmPassword) {
      setErrorMessage("Passwords and Confirm Password do not match");
      return false;
    }

    return true;
  };

  const handleSignUp = async () => {
    try {
      const res = validate();
      if (!res) {
        return;
      }
      setErrorMessage("");
      setFailed(false);
      setSuccess(false);
      setLoading(true);
      const deviceId = await getUniqueId();
      const data = {
        email: email,
        username: route.params.number,
        phoneNumber: route.params.number,
        nationalIdNumber: nationalId.toLocaleUpperCase(),
        /*           .replace(/([A-Z])/g, " $1 "), */
        password: password,
        agent: UserAgent.getUserAgent(),
        signupPlatform: "MOBILE-APP",
        deviceId: deviceId + parseInt(route.params.number),
        service: "NETONE-MOBILE-APP",
      };
      console.log(data);

      const store = await LocalStore.saveData("@username", route.params.number);
      if (store == "success") {
        const response = await AuthService.register(data);

        if (response.data.success) {
          if (response.data.body == null) {
            setFailed(true);
            setLoading(false);
            setErrorMessage(response.data.message);
          } else {
            setEmail("");
            setNationalID("");
            setPassword("");
            setConfirmPassword("");
            setLoading(false);
            setSuccess(true);
            setErrorMessage("Account created, you can proceed to login");
          }
        } else {
          setFailed(true);
          setErrorMessage(response.data.message);
          setLoading(false);
        }
      } else {
        setLoading(false);
        setFailed(true);
        setErrorMessage("Something went wrong, Please try again");
      }
    } catch (error) {
      setLoading(false);
      setFailed(true);
      console.log(error);
      if (error.response) {
        if (error.response.status == 400) {
          setErrorMessage("Something went wrong, Please try again");
        } else if (
          error.response.status == 403 ||
          error.response.status == 500 ||
          error.response.status == 504
        ) {
          setErrorMessage("Server Error, Please try again later");
        } else {
          if (error.response.data.message) {
            setErrorMessage(error.response.data.message);
          } else {
            setErrorMessage("Something went wrong, Please try again");
          }
        }
      } else if (error.request) {
        setErrorMessage("Network Error, Please check your internet connection");
      } else {
        setErrorMessage("Something went wrong, Please try again");
      }
    }
  };

  const handleInputChange = (text) => {
    const regex = /^([0-9][0-9])-([0-9]{6}|[0-9]{7})([a-zA-Z])([0-9]{2})$/;
    setIdField(regex.test(text));
    setNationalID(text);
  };

  const handleEmailChange = (text) => {
    const emailRegExp = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    setEmailField(emailRegExp.test(text));
    setEmail(text);
  };

  useEffect(() => {}, [loading]);

  return (
    <KeyboardAwareScrollView
      bg={"white"}
      showsVerticalScrollIndicator={false}
      extraScrollHeight={100}>
      <LoadingModal isLoading={loading} />
      {success && <AccountCreated></AccountCreated>}

      <View w="100%" h={"100%"} px={moderateScale(24)}>
        <Toast
          visible={failed}
          position={30}
          shadow={true}
          animation={true}
          hideOnPress={true}
          backgroundColor={"red"}
          opacity={0.9}>
          {errorMessage}
        </Toast>
        <Box safeArea w="100%" py={moderateScale(24)} mb={moderateScale(24)}>
          <Center>
            <View p={moderateScale(4)}>
              <Image
                source={netone}
                height={verticalScale(100)}
                width={scale(150)}
                alt={"OneMoney"}
              />
            </View>

            <Heading
              fontSize={moderateScale(20)}
              mt={moderateScale(50)}
              fontFamily={"openSansSemiBold"}
              fontWeight="600"
              color="coolGray.800">
              Create your account
            </Heading>
            <Heading
              fontSize={moderateScale(12)}
              mt={moderateScale(10)}
              fontFamily={"openSansMedium"}
              fontWeight="400"
              color="orange.500">
              Note: You can also use these details to login to the oneMoney
              mobile app
            </Heading>
          </Center>

          <VStack space={moderateScale(16)} mt={moderateScale(48)}>
            <Heading
              fontSize={moderateScale(15)}
              mt={moderateScale(0)}
              fontFamily={"openSansSemiBold"}
              fontWeight="600"
              color="coolGray.800">
              Phone Number: 263{route.params.number}
            </Heading>
            <FormControl>
              <Input
                fontFamily={"openSansSemiBold"}
                onFocus={() => {
                  setEmailError("");
                }}
                focusOutlineColor={"#F68C1E"}
                borderColor={emailError ? "red.400" : "gray.300"}
                placeholder="Email Address"
                fontSize={moderateScale(15)}
                bgColor={"gray.100"}
                rounded={"lg"}
                onChangeText={(text) => handleEmailChange(text)}
                h={verticalScale(48)}
                keyboardType={"email-address"}
              />
              <Text
                fontFamily={"openSansMedium"}
                color={"gray.600"}
                fontSize={moderateScale(13)}>
                (This Field is optional)
              </Text>
              {emailField == false && email ? (
                <Text color={"red.500"}>Invalid Email</Text>
              ) : (
                <></>
              )}
              <Text color={"red.400"} fontSize={moderateScale(15)}>
                {emailError}
              </Text>
            </FormControl>

            <FormControl>
              <Text
                fontFamily={"openSans"}
                color={"blue.600"}
                fontSize={moderateScale(15)}>
                Use this format: 00-000000B00
              </Text>
              <Input
                fontFamily={"openSansSemiBold"}
                onFocus={() => {
                  setNationalIDError("");
                }}
                focusOutlineColor={"#F68C1E"}
                borderColor={nationalIdError ? "red.400" : "gray.300"}
                placeholder="National ID Number"
                fontSize={moderateScale(15)}
                bgColor={"gray.100"}
                rounded={"lg"}
                onChangeText={handleInputChange}
                h={verticalScale(48)}
              />
              <Text color={"red.400"} fontSize={moderateScale(15)}>
                {nationalIdError}
              </Text>
            </FormControl>

            {/*             <FormControl>
              <InputGroup>
                <InputLeftAddon
                  isDisabled={phone ? true : false}
                  roundedLeft={"lg"}
                  w={scale(60)}
                  children={
                    <Text
                      fontSize={moderateScale(15)}
                      fontFamily={"openSansMedium"}
                      color={"gray.600"}>
                      {"+263"}
                    </Text>
                  }
                />
                <Input
                  flex={1}
                  defaultValue={`${phone}`}
                  fontFamily={"openSansSemiBold"}
                  onFocus={() => {
                    setPhoneError("");
                  }}
                  focusOutlineColor={"#F68C1E"}
                  borderColor={phoneError ? "red.400" : "gray.300"}
                  placeholder={"Netone Phone Number"}
                  fontSize={moderateScale(15)}
                  bgColor={"gray.100"}
                  rounded={"lg"}
                  onChangeText={(text) => setPhone(text)}
                  h={verticalScale(48)}
                  keyboardType={"number-pad"}
                />
              </InputGroup>
              <Text color={"red.400"} fontSize={moderateScale(15)}>
                {phoneError}
              </Text>
            </FormControl> */}

            <FormControl>
              <InputGroup>
                <Input
                  flex={1}
                  secureTextEntry={showpassword}
                  fontFamily={"openSansSemiBold"}
                  onFocus={() => {
                    setPasswordError("");
                  }}
                  focusOutlineColor={"#F68C1E"}
                  borderColor={passwordError ? "red.400" : "gray.300"}
                  placeholder="Password"
                  fontSize={moderateScale(15)}
                  bgColor={"gray.100"}
                  rounded={"lg"}
                  onChangeText={(text) => setPassword(text)}
                  h={verticalScale(48)}
                  keyboardType={"default"}
                />
                <InputRightAddon
                  roundedRight={"lg"}
                  w={scale(40)}
                  children={
                    <Feather
                      name={showpassword ? "eye-off" : "eye"}
                      size={moderateScale(20)}
                      color="gray"
                      onPress={() => {
                        setShowPassword(!showpassword);
                      }}
                    />
                  }
                />
              </InputGroup>
              <Text color={"red.400"} fontSize={moderateScale(15)}>
                {passwordError}
              </Text>
            </FormControl>

            <FormControl>
              <InputGroup>
                <Input
                  flex={1}
                  secureTextEntry={showconfirm}
                  fontFamily={"openSansSemiBold"}
                  onFocus={() => {
                    setConfirmPasswordError("");
                  }}
                  focusOutlineColor={"#F68C1E"}
                  borderColor={confirmPasswordError ? "red.400" : "gray.300"}
                  placeholder="Confirm Password"
                  fontSize={moderateScale(15)}
                  bgColor={"gray.100"}
                  rounded={"lg"}
                  onChangeText={(text) => setConfirmPassword(text)}
                  h={verticalScale(48)}
                  keyboardType={"default"}
                />
                <InputRightAddon
                  roundedRight={"lg"}
                  w={scale(40)}
                  children={
                    <Feather
                      name={showconfirm ? "eye-off" : "eye"}
                      size={moderateScale(20)}
                      color="gray"
                      onPress={() => {
                        setShowConfirm(!showconfirm);
                      }}
                    />
                  }
                />
              </InputGroup>
              <Text color={"red.400"} fontSize={moderateScale(15)}>
                {confirmPasswordError}
              </Text>
            </FormControl>
            {/*   <FormControl>
              <TextArea
                fontFamily={"openSansSemiBold"}
                onFocus={() => {
                  setPassphrase("");
                }}
                focusOutlineColor={"#F68C1E"}
                borderColor={passphraseError ? "red.400" : "gray.300"}
                placeholder="Set Pass-Phrase"
                numberOfLines={4}
                fontSize={moderateScale(15)}
                bgColor={"gray.100"}
                rounded={"lg"}
                onChangeText={(text) => setPassphrase(text)}
                h={verticalScale(100)}
                mb={moderateScale(2)}
              />

              <CustomTooltip text="This a phrase that will be used when you forget your pin to reset it.Make sure you will be able to remember it.">
                <Feather name="info" size={moderateScale(20)} color="gray" />
              </CustomTooltip>
              <Text color={"red.400"} fontSize={moderateScale(15)}>
                {passphraseError}
              </Text>
            </FormControl> */}

            <Button
              mt={moderateScale(24)}
              onPress={() => {
                handleSignUp();
              }}
              h={verticalScale(48)}
              bg="#F68C1E"
              rounded={"lg"}>
              <Text
                fontSize={moderateScale(15)}
                color="white"
                fontFamily={"openSansSemiBold"}>
                Sign Up
              </Text>
            </Button>
          </VStack>
        </Box>
      </View>
    </KeyboardAwareScrollView>
  );
};

export default SignUp;
