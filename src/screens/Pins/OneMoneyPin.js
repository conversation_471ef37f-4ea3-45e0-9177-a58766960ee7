import { useNavigation } from "@react-navigation/native";
import {
  Box,
  Button,
  FormControl,
  Input,
  InputGroup,
  Text,
  VStack,
  View,
} from "native-base";
import React, { useState } from "react";
import { Dimensions, StyleSheet } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { moderateScale, verticalScale } from "react-native-size-matters";

const OneMoneyPin = ({ route }) => {
  const [oldPin, setOldPin] = useState("");
  const [errorOldPin, setErrorOldPin] = useState("");
  const [newPin, setNewPin] = useState("");
  const [errorNewPin, setErrorNewPin] = useState("");
  const [confirmPin, setConfirmPin] = useState("");
  const [errorConfirmPin, setErrorConfirmPin] = useState("");
  const [errorMessage, setErrorMessage] = useState("");
  const [loading, setLoading] = useState(false);
  const height = Dimensions.get("window").height;
  const [failed, setFailed] = useState(false);
  const [success, setSuccess] = useState(false);
  const [successMessage, setSuccessMessage] = useState("");
  const navigation = useNavigation();

  const validate = () => {
    if (oldPin === "") {
      setErrorOldPin("Old Pin is required");
      return false;
    }
    if (newPin === "") {
      setErrorNewPin("New Pin is required");
      return false;
    }
    if (confirmPin === "") {
      setErrorConfirmPin("Confirm Pin is required");
      return false;
    }
    if (newPin !== confirmPin) {
      setErrorConfirmPin("New Pin and Confirm Pin do not match");
      return false;
    }
    return true;
  };
  /* 
  const handleChangePin = async () => {
    try {
      const res = validate();
      if (!res) {
        return;
      }
      setLoading(true);
      setFailed(false);

      const allData = {
        newPin: newPin,
        oldPin: oldPin,
        phoneNumber: route.params.phone,
      };

      const response = await Auth.changePin(allData);
      if (response.data.success) {
        setSuccess(true);
        setSuccessMessage("Pin changed successfully");
        setTimeout(() => {
          navigation.navigate("Signin");
        }, 2000);
      } else {
        setFailed(true);
        setErrorMessage("Something went wrong, Please try again");
      }
      setLoading(false);
      setOldPin("");
      setNewPin("");
      setConfirmPin("");
      setErrorMessage("");
    } catch (error) {
      setLoading(false);
      setFailed(true);
      if (error.response) {
        if (error.response.status == 400) {
          setErrorMessage("Something went wrong, Please try again");
        } else if (
          error.response.status == 403 ||
          error.response.status == 500 ||
          error.response.status == 504
        ) {
          setErrorMessage("Server Error, Please try again later");
        } else {
          if (error.response.data.message) {
            setErrorMessage(error.response.data.message);
          } else {
            setErrorMessage("Something went wrong, Please try again");
          }
        }
      } else if (error.request) {
        setErrorMessage("Network Error, Please check your internet connection");
      } else {
        setErrorMessage("Something went wrong, Please try again");
      }
    }
  }; */

  return (
    <SafeAreaView>
      <View w="100%" bg={"white"} h={height}>
        <Box px={moderateScale(20)} py={moderateScale(10)} w="100%">
          <VStack space={moderateScale(16)}>
            <FormControl>
              <InputGroup>
                <Input
                  flex={1}
                  defaultValue={`${oldPin}`}
                  fontFamily={"openSansSemiBold"}
                  onFocus={() => {
                    setErrorOldPin("");
                  }}
                  focusOutlineColor={"#F68C1E"}
                  borderColor={errorOldPin ? "red.400" : "gray.300"}
                  placeholder="Old Pin"
                  fontSize={moderateScale(15)}
                  bgColor={"gray.100"}
                  rounded={"lg"}
                  onChangeText={(text) => setOldPin(text)}
                  h={verticalScale(48)}
                  keyboardType={"number-pad"}
                />
              </InputGroup>

              <Text color={"red.400"} fontSize={moderateScale(15)}>
                {errorOldPin}
              </Text>
            </FormControl>
            <FormControl>
              <InputGroup>
                <Input
                  flex={1}
                  defaultValue={`${newPin}`}
                  fontFamily={"openSansSemiBold"}
                  onFocus={() => {
                    setErrorNewPin("");
                  }}
                  focusOutlineColor={"#F68C1E"}
                  borderColor={errorNewPin ? "red.400" : "gray.300"}
                  placeholder="New Pin"
                  fontSize={moderateScale(15)}
                  bgColor={"gray.100"}
                  rounded={"lg"}
                  onChangeText={(text) => setNewPin(text)}
                  h={verticalScale(48)}
                  keyboardType={"number-pad"}
                />
              </InputGroup>
              <Text color={"red.400"} fontSize={moderateScale(15)}>
                {errorNewPin}
              </Text>
            </FormControl>
            <FormControl>
              <InputGroup>
                <Input
                  flex={1}
                  defaultValue={`${confirmPin}`}
                  fontFamily={"openSansSemiBold"}
                  onFocus={() => {
                    setErrorConfirmPin("");
                  }}
                  focusOutlineColor={"#F68C1E"}
                  borderColor={errorConfirmPin ? "red.400" : "gray.300"}
                  placeholder="Confirm New Pin"
                  fontSize={moderateScale(15)}
                  bgColor={"gray.100"}
                  rounded={"lg"}
                  onChangeText={(text) => setConfirmPin(text)}
                  h={verticalScale(48)}
                  keyboardType={"number-pad"}
                />
              </InputGroup>
              <Text color={"red.400"} fontSize={moderateScale(15)}>
                {errorConfirmPin}
              </Text>
            </FormControl>

            <Button
              onPress={() => handleChangePin()}
              mt={moderateScale(24)}
              h={verticalScale(48)}
              bg="#F68C1E"
              rounded={"lg"}>
              <Text
                fontSize={moderateScale(15)}
                color="white"
                fontFamily={"openSansSemiBold"}>
                Change
              </Text>
            </Button>
          </VStack>
        </Box>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: "center",
    padding: 20,
  },
  label: {
    fontSize: 16,
    marginTop: 20,
  },
  input: {
    width: "100%",
    height: 40,
    borderColor: "gray",
    borderWidth: 1,
    marginTop: 10,
    padding: 10,
  },
  button: {
    marginTop: 20,
    backgroundColor: "blue",
    padding: 10,
  },
  buttonText: {
    color: "white",
    fontSize: 16,
  },
  errorMessage: {
    marginTop: 20,
    color: "red",
    fontSize: 16,
  },
});

export default OneMoneyPin;
