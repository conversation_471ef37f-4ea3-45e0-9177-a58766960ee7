import { useNavigation } from "@react-navigation/native";
import {
  Box,
  Button,
  FormControl,
  Heading,
  Input,
  InputGroup,
  Text,
  VStack,
  View,
} from "native-base";
import React, { useState } from "react";
import { Dimensions, StyleSheet } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { moderateScale, verticalScale } from "react-native-size-matters";
import LoadingModal from "../../components/Loading/LoadingModal";

const ChangePassword = ({ route }) => {
  const [oldPassword, setOldPassword] = useState("");
  const [errorOldPassword, setErrorOldPassword] = useState("");
  const [newPassword, setNewPassword] = useState("");
  const [errorNewPassword, setErrorNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [errorConfirmPassword, setErrorConfirmPassword] = useState("");
  const [errorMessage, setErrorMessage] = useState("");
  const [loading, setLoading] = useState(false);
  const height = Dimensions.get("window").height;
  const [failed, setFailed] = useState(false);
  const [success, setSuccess] = useState(false);
  const [successMessage, setSuccessMessage] = useState("");
  const navigation = useNavigation();

  const validate = () => {
    if (oldPassword === "") {
      setErrorOldPassword("Old Password is required");
      return false;
    }
    if (newPassword === "") {
      setErrorNewPassword("New Password is required");
      return false;
    }
    if (confirmPassword === "") {
      setErrorConfirmPassword("Confirm Password is required");
      return false;
    }
    if (newPassword !== confirmPassword) {
      setErrorConfirmPassword("New Password and Confirm Password do not match");
      return false;
    }
    return true;
  };

  const handleChangePassword = async () => {
    try {
      const res = validate();
      if (!res) {
        return;
      }
      setLoading(true);
      setFailed(false);

      const allData = {
        newPassword: newPassword,
        oldPassword: oldPassword,
        phoneNumber: route.params.phone,
      };

      const response = await Auth.changePassword(allData);
      if (response.data.success) {
        setSuccess(true);
        setSuccessMessage("Password changed successfully");
        setTimeout(() => {
          navigation.navigate("Signin");
        }, 2000);
      } else {
        setFailed(true);
        setErrorMessage("Something went wrong, Please try again");
      }
      setLoading(false);
      setOldPassword("");
      setNewPassword("");
      setConfirmPassword("");
      setErrorMessage("");
    } catch (error) {
      setLoading(false);
      setFailed(true);
      if (error.response) {
        if (error.response.status == 400) {
          setErrorMessage("Something went wrong, Please try again");
        } else if (
          error.response.status == 403 ||
          error.response.status == 500 ||
          error.response.status == 504
        ) {
          setErrorMessage("Server Error, Please try again later");
        } else {
          if (error.response.data.message) {
            setErrorMessage(error.response.data.message);
          } else {
            setErrorMessage("Something went wrong, Please try again");
          }
        }
      } else if (error.request) {
        setErrorMessage("Network Error, Please check your internet connection");
      } else {
        setErrorMessage("Something went wrong, Please try again");
      }
    }
  };

  return (
    <SafeAreaView>
      <View w="100%" bg={"white"} h={height}>
        <Box px={moderateScale(20)} py={moderateScale(10)} w="100%">
          <Heading
            fontSize={moderateScale(12)}
            mt={moderateScale(10)}
            fontFamily={"openSansMedium"}
            fontWeight="400"
            color="orange.500">
            Note: You are changing your ONE account password.
          </Heading>
          <LoadingModal isLoading={loading} />
          <VStack space={moderateScale(16)} mt={moderateScale(38)}>
            <FormControl>
              <InputGroup>
                <Input
                  flex={1}
                  defaultValue={`${oldPassword}`}
                  fontFamily={"openSansSemiBold"}
                  onFocus={() => {
                    setErrorOldPassword("");
                  }}
                  focusOutlineColor={"#F68C1E"}
                  borderColor={errorOldPassword ? "red.400" : "gray.300"}
                  placeholder="Old Password"
                  fontSize={moderateScale(15)}
                  bgColor={"gray.100"}
                  rounded={"lg"}
                  onChangeText={(text) => setOldPassword(text)}
                  h={verticalScale(48)}
                  keyboardType={"number-pad"}
                />
              </InputGroup>

              <Text color={"red.400"} fontSize={moderateScale(15)}>
                {errorOldPassword}
              </Text>
            </FormControl>
            <FormControl>
              <InputGroup>
                <Input
                  flex={1}
                  defaultValue={`${newPassword}`}
                  fontFamily={"openSansSemiBold"}
                  onFocus={() => {
                    setErrorNewPassword("");
                  }}
                  focusOutlineColor={"#F68C1E"}
                  borderColor={errorNewPassword ? "red.400" : "gray.300"}
                  placeholder="New Password"
                  fontSize={moderateScale(15)}
                  bgColor={"gray.100"}
                  rounded={"lg"}
                  onChangeText={(text) => setNewPassword(text)}
                  h={verticalScale(48)}
                  keyboardType={"number-pad"}
                />
              </InputGroup>
              <Text color={"red.400"} fontSize={moderateScale(15)}>
                {errorNewPassword}
              </Text>
            </FormControl>
            <FormControl>
              <InputGroup>
                <Input
                  flex={1}
                  defaultValue={`${confirmPassword}`}
                  fontFamily={"openSansSemiBold"}
                  onFocus={() => {
                    setErrorConfirmPassword("");
                  }}
                  focusOutlineColor={"#F68C1E"}
                  borderColor={errorConfirmPassword ? "red.400" : "gray.300"}
                  placeholder="Confirm New Password"
                  fontSize={moderateScale(15)}
                  bgColor={"gray.100"}
                  rounded={"lg"}
                  onChangeText={(text) => setConfirmPassword(text)}
                  h={verticalScale(48)}
                  keyboardType={"number-pad"}
                />
              </InputGroup>
              <Text color={"red.400"} fontSize={moderateScale(15)}>
                {errorConfirmPassword}
              </Text>
            </FormControl>

            <Button
              onPress={() => handleChangePassword()}
              mt={moderateScale(24)}
              h={verticalScale(48)}
              bg="#F68C1E"
              rounded={"lg"}>
              <Text
                fontSize={moderateScale(15)}
                color="white"
                fontFamily={"openSansSemiBold"}>
                Change
              </Text>
            </Button>
          </VStack>
        </Box>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: "center",
    padding: 20,
  },
  label: {
    fontSize: 16,
    marginTop: 20,
  },
  input: {
    width: "100%",
    height: 40,
    borderColor: "gray",
    borderWidth: 1,
    marginTop: 10,
    padding: 10,
  },
  button: {
    marginTop: 20,
    backgroundColor: "blue",
    padding: 10,
  },
  buttonText: {
    color: "white",
    fontSize: 16,
  },
  errorMessage: {
    marginTop: 20,
    color: "red",
    fontSize: 16,
  },
});

export default ChangePassword;
