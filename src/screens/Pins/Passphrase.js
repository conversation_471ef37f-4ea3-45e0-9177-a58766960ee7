import { useNavigation } from "@react-navigation/native";
import {
  Box,
  Button,
  FormControl,
  Input,
  InputGroup,
  Text,
  TextArea,
  VStack,
  View,
} from "native-base";
import React, { useState } from "react";
import { Dimensions, StyleSheet } from "react-native";

import { SafeAreaView } from "react-native-safe-area-context";
import { moderateScale, verticalScale } from "react-native-size-matters";

const ChangePassPhrase = ({ route }) => {
  const [oldPassphrase, setOldPassphrase] = useState("");
  const [errorOldPassphrase, setErrorOldPassphrase] = useState("");
  const [newPassphrase, setNewPassphrase] = useState("");
  const [errorNewPassphrase, setErrorNewPassphrase] = useState("");
  const [confirmPassphrase, setConfirmPassphrase] = useState("");
  const [errorConfirmPassphrase, setErrorConfirmPassphrase] = useState("");
  const [errorMessage, setErrorMessage] = useState("");
  const [loading, setLoading] = useState(false);
  const height = Dimensions.get("window").height;
  const [failed, setFailed] = useState(false);
  const [success, setSuccess] = useState(false);
  const [successMessage, setSuccessMessage] = useState("");
  const navigation = useNavigation();

  const validate = () => {
    if (oldPassphrase === "") {
      setErrorOldPassphrase("Old Passphrase is required");
      return false;
    }
    if (newPassphrase === "") {
      setErrorNewPassphrase("New Passphrase is required");
      return false;
    }
    if (confirmPassphrase === "") {
      setErrorConfirmPassphrase("Confirm Passphrase is required");
      return false;
    }
    if (newPassphrase !== confirmPassphrase) {
      setErrorConfirmPassphrase(
        "New Passphrase and Confirm Passphrase do not match"
      );
      return false;
    }
    return true;
  };

  /*   const handleChangePassphrase = async () => {
    try {
      const res = validate();
      if (!res) {
        return;
      }
      setLoading(true);
      setFailed(false);

      const allData = {
        newPassphrase: newPassphrase,
        oldPassphrase: oldPassphrase,
        phoneNumber: route.params.phone,
      };

      const response = await Auth.changePassphrase(allData);
      if (response.data.success) {
        setSuccess(true);
        setSuccessMessage("Passphrase changed successfully");
        setTimeout(() => {
          navigation.navigate("Signin");
        }, 2000);
      } else {
        setFailed(true);
        setErrorMessage("Something went wrong, Please try again");
      }
      setLoading(false);
      setOldPassphrase("");
      setNewPassphrase("");
      setConfirmPassphrase("");
      setErrorMessage("");
    } catch (error) {
      setLoading(false);
      setFailed(true);
      if (error.response) {
        if (error.response.status == 400) {
          setErrorMessage("Something went wrong, Please try again");
        } else if (
          error.response.status == 403 ||
          error.response.status == 500 ||
          error.response.status == 504
        ) {
          setErrorMessage("Server Error, Please try again later");
        } else {
          if (error.response.data.message) {
            setErrorMessage(error.response.data.message);
          } else {
            setErrorMessage("Something went wrong, Please try again");
          }
        }
      } else if (error.request) {
        setErrorMessage("Network Error, Please check your internet connection");
      } else {
        setErrorMessage("Something went wrong, Please try again");
      }
    }
  }; */

  return (
    <SafeAreaView>
      <View w="100%" bg={"white"} h={height}>
        <Box px={moderateScale(20)} py={moderateScale(10)} w="100%">
          <VStack space={moderateScale(16)}>
            <FormControl>
              <InputGroup>
                <TextArea
                  flex={1}
                  defaultValue={`${oldPassphrase}`}
                  fontFamily={"openSansSemiBold"}
                  onFocus={() => {
                    setErrorOldPassphrase("");
                  }}
                  focusOutlineColor={"#F68C1E"}
                  borderColor={errorOldPassphrase ? "red.400" : "gray.300"}
                  placeholder="Old Passphrase"
                  fontSize={moderateScale(15)}
                  bgColor={"gray.100"}
                  rounded={"lg"}
                  onChangeText={(text) => setOldPassphrase(text)}
                  h={verticalScale(100)}
                  keyboardType={"number-pad"}
                />
              </InputGroup>

              <Text color={"red.400"} fontSize={moderateScale(15)}>
                {errorOldPassphrase}
              </Text>
            </FormControl>
            <FormControl>
              <InputGroup>
                <TextArea
                  flex={1}
                  defaultValue={`${newPassphrase}`}
                  fontFamily={"openSansSemiBold"}
                  onFocus={() => {
                    setErrorNewPassphrase("");
                  }}
                  focusOutlineColor={"#F68C1E"}
                  borderColor={errorNewPassphrase ? "red.400" : "gray.300"}
                  placeholder="New Passphrase"
                  fontSize={moderateScale(15)}
                  bgColor={"gray.100"}
                  rounded={"lg"}
                  onChangeText={(text) => setNewPassphrase(text)}
                  h={verticalScale(100)}
                  keyboardType={"number-pad"}
                />
              </InputGroup>
              <Text color={"red.400"} fontSize={moderateScale(15)}>
                {errorNewPassphrase}
              </Text>
            </FormControl>
            <FormControl>
              <InputGroup>
                <TextArea
                  flex={1}
                  defaultValue={`${confirmPassphrase}`}
                  fontFamily={"openSansSemiBold"}
                  onFocus={() => {
                    setErrorConfirmPassphrase("");
                  }}
                  focusOutlineColor={"#F68C1E"}
                  borderColor={errorConfirmPassphrase ? "red.400" : "gray.300"}
                  placeholder="Confirm New Passphrase"
                  fontSize={moderateScale(15)}
                  bgColor={"gray.100"}
                  rounded={"lg"}
                  onChangeText={(text) => setConfirmPassphrase(text)}
                  h={verticalScale(100)}
                  keyboardType={"number-pad"}
                />
              </InputGroup>
              <Text color={"red.400"} fontSize={moderateScale(15)}>
                {errorConfirmPassphrase}
              </Text>
            </FormControl>

            <Button
              onPress={() => handleChangePassphrase()}
              mt={moderateScale(24)}
              h={verticalScale(48)}
              bg="#F68C1E"
              rounded={"lg"}>
              <Text
                fontSize={moderateScale(15)}
                color="white"
                fontFamily={"openSansSemiBold"}>
                Change
              </Text>
            </Button>
          </VStack>
        </Box>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: "center",
    padding: 20,
  },
  label: {
    fontSize: 16,
    marginTop: 20,
  },
  input: {
    width: "100%",
    height: 40,
    borderColor: "gray",
    borderWidth: 1,
    marginTop: 10,
    padding: 10,
  },
  button: {
    marginTop: 20,
    backgroundColor: "blue",
    padding: 10,
  },
  buttonText: {
    color: "white",
    fontSize: 16,
  },
  errorMessage: {
    marginTop: 20,
    color: "red",
    fontSize: 16,
  },
});

export default ChangePassPhrase;
