import { MaterialCommunityIcons } from "@expo/vector-icons";
import { useNavigation } from "@react-navigation/native";
import { Box, HStack, ScrollView, StatusBar, Text, VStack } from "native-base";
import React, { useEffect, useState } from "react";
import { SafeAreaView, TouchableOpacity } from "react-native";
import { moderateScale } from "react-native-size-matters";
import LocalStore from "../../utilities/store";

const SelfCare = ({ route }) => {
  const navigation = useNavigation();
  const [profile, setProfile] = useState(null);

  useEffect(() => {
    const getProfile = async () => {
      const profile = await LocalStore.getData("@userProfile");
      if (profile !== null) {
        setProfile(profile);
      }
    };
    getProfile();
  }, []);

  const services = [
    {
      key: 2,
      name: "Request Puk",
      route: "requestPuk",
      ref: "PUK",
      icon: "arrow-vertical-lock",
      bgColor: "#F68C1E",
      description: "Unlock your SIM card",
    },
    {
      key: 3,
      name: "Linked Numbers",
      route: "LinkedNumbers",
      ref: "LINKED-NUMBERS",
      icon: "link",
      bgColor: "#10B981",
      description: "Manage linked numbers",
    },
    {
      key: 4,
      name: "Passphrase",
      route: "passphrase",
      ref: "PASSPHRASE",
      icon: "form-textbox-password",
      bgColor: "#3B82F6",
      description: "Change your passphrase",
    },
    {
      key: 5,
      name: "USSD Codes",
      route: "ussdCodes",
      ref: "USSD-CODES",
      icon: "dialpad",
      bgColor: "#8B5CF6",
      description: "Quick access codes",
    },
  ];

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: "white" }}>
      <StatusBar backgroundColor={"white"} barStyle="dark-content" />
      <ScrollView flex={1} bg={"gray.50"} showsVerticalScrollIndicator={false}>
        {/* Header */}
        <Box bg={"white"} px={moderateScale(20)} pt={moderateScale(10)}>
          <VStack space={moderateScale(16)} py={moderateScale(16)}>
            <Text
              fontFamily={"openSansBold"}
              fontSize={moderateScale(28)}
              color={"gray.900"}>
              Self Care
            </Text>
            <Text
              fontFamily={"openSansMedium"}
              fontSize={moderateScale(16)}
              color={"gray.500"}>
              Manage your account settings
            </Text>
          </VStack>
        </Box>

        {/* Services Grid */}
        <Box px={moderateScale(20)} py={moderateScale(20)}>
          <VStack space={moderateScale(16)}>
            {/* Service Cards */}
            <VStack space={moderateScale(12)}>
              {services.map((item) => (
                <TouchableOpacity
                  key={item.key}
                  onPress={() => {
                    navigation.navigate(item.route, {
                      options: [],
                      title: item.name,
                      ref: item.ref,
                      phone: profile?.phoneNumber,
                    });
                  }}>
                  <Box
                    bg={"white"}
                    rounded={"2xl"}
                    p={moderateScale(20)}
                    shadow={2}
                    borderWidth={1}
                    borderColor={"gray.100"}>
                    <HStack space={moderateScale(16)} alignItems={"center"}>
                      <Box
                        bg={item.bgColor}
                        rounded={"full"}
                        w={moderateScale(48)}
                        h={moderateScale(48)}
                        alignItems={"center"}
                        justifyContent={"center"}>
                        <MaterialCommunityIcons
                          name={item.icon}
                          size={moderateScale(24)}
                          color="white"
                        />
                      </Box>
                      <VStack flex={1} space={moderateScale(4)}>
                        <Text
                          fontFamily={"openSansBold"}
                          fontSize={moderateScale(16)}
                          color={"gray.900"}>
                          {item.name}
                        </Text>
                        <Text
                          fontFamily={"openSansMedium"}
                          fontSize={moderateScale(14)}
                          color={"gray.500"}>
                          {item.description}
                        </Text>
                      </VStack>
                      <MaterialCommunityIcons
                        name="chevron-right"
                        size={moderateScale(20)}
                        color="#9CA3AF"
                      />
                    </HStack>
                  </Box>
                </TouchableOpacity>
              ))}
            </VStack>
          </VStack>
        </Box>
      </ScrollView>
    </SafeAreaView>
  );
};

export default SelfCare;
