import { Entypo } from "@expo/vector-icons";
import { useNavigation } from "@react-navigation/native";
import { Text, View } from "native-base";
import React, { useEffect, useState } from "react";
import { FlatList, StyleSheet, TouchableOpacity } from "react-native";
import { moderateScale } from "react-native-size-matters";
import LocalStore from "../../utilities/store";

const SelfCare = ({ route }) => {
  const navigation = useNavigation();
  const [profile, setProfile] = useState(null);
  useEffect(() => {
    const getProfile = async () => {
      const profile = await LocalStore.getData("@userProfile");

      if (profile !== null) {
        setProfile(profile);
      }
    };
    getProfile();
  }, []);
  const data = [
    { key: 2, name: "Request Puk", route: "requestPuk", ref: "PUK" },
    {
      key: 3,
      name: "Linked Numbers",
      route: "LinkedNumbers",
      ref: "LINKED-NUMBERS",
    },
    { key: 4, name: "Passphrase", route: "passphrase", ref: "PASSPHRASE" },
    { key: 5, name: "USSD Codes", route: "ussdCodes", ref: "USSD-CODES" },
  ];

  const renderRow = (item) => {
    return (
      <TouchableOpacity
        onPress={() => {
          navigation.navigate(item.route, {
            options: [],
            title: item.name,
            ref: item.ref,
            phone: profile?.phoneNumber,
          });
        }}>
        <View
          style={styles.itemContainer}
          bgColor={"white"}
          alignItems={"center"}
          mx={moderateScale(16)}
          my={moderateScale(8)}
          rounded={"xl"}
          shadow={5}>
          {/*    <Image source={item.image} style={styles.image}/> */}
          <Text
            fontFamily={"openSansMedium"}
            fontWeight="500"
            color={"gray.600"}
            flex={1}
            fontSize={moderateScale(16)}>
            {item.name}
          </Text>
          <Entypo
            name="chevron-small-right"
            size={moderateScale(24)}
            color="gray"
          />
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <View style={styles.container} pt={moderateScale(50)}>
      <FlatList
        data={data}
        renderItem={({ item }) => renderRow(item)}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ paddingBottom: moderateScale(100) }}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  itemContainer: {
    flexDirection: "row",
    alignItems: "center",
    padding: 20,
    borderRadius: 10,
  },
  itemName: {
    flex: 1,
  },
  container: {
    flex: 1,
    backgroundColor: "white",
    padding: 10,
  },
  image: {
    width: 30,
    height: 30,
    tintColor: "#F68C1E",
    marginRight: 10,
  },
});

export default SelfCare;
