import { useNavigation } from "@react-navigation/native";
import {
  Box,
  Button,
  FormControl,
  Heading,
  Input,
  InputGroup,
  InputLeftAddon,
  Text,
  VStack,
  View,
} from "native-base";
import React, { useEffect, useState } from "react";
import Toast from "react-native-root-toast";
import { moderateScale, scale, verticalScale } from "react-native-size-matters";
import LoadingModal from "../components/Loading/LoadingModal";
import AuthService from "../services/Auth/Auth";

const PhoneNumber = (props) => {
  const navigation = useNavigation();
  const [phone, setPhone] = useState("");
  const [phoneError, setPhoneError] = useState("");
  const [loading, setLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const [failed, setFailed] = useState(false);

  useEffect(() => {
    const interval = setTimeout(() => {
      setFailed(false);
    }, 5000);

    return () => clearInterval(interval);
  }, [failed]);

  const checkNumber = (num) => {
    const numberRegexp2 = /^(0712980|712980)/;
    if (numberRegexp2.test(num) === true) {
      return true;
    } else {
      return false;
      /*       const number = parseInt(num)
      return numbers.includes(number) */
    }
  };

  const validate = () => {
    const numberRegexp = /^(071|71)/;

    if (!phone) {
      setPhoneError("This field is required");
      return false;
    }

    if (phone.length < 9) {
      setPhoneError("Invalid phone number, not enough digits");
      return false;
    }

    if (phone.length > 10) {
      setPhoneError("Invalid phone number, too many digits");
      return false;
    }

    if (!numberRegexp.test(phone)) {
      setPhoneError("Invalid phone number, should be a netone number");
      return false;
    }
    return true;
  };

  const sendPhoneNumber = async () => {
    try {
      const res = validate();
      if (!res) {
        return;
      }
      setLoading(true);
      setFailed(false);
      const response = await AuthService.GenerateOtp(parseInt(phone));

      if (response.data.success) {
        navigation.navigate("otp", {
          number: parseInt(phone),
          to: props.route.params.to,
        });
        setLoading(false);
      } else {
        setFailed(true);
        setErrorMessage("Failed to send code");
        setLoading(false);
      }
    } catch (error) {
      setLoading(false);
      setFailed(true);
      if (error.response) {
        if (error.response.status == 400) {
          setErrorMessage("Something went wrong, Please try again");
        } else if (
          error.response.status == 403 ||
          error.response.status == 500 ||
          error.response.status == 504
        ) {
          setErrorMessage("Server Error, Please try again later");
        } else {
          if (error.response.data.message) {
            setErrorMessage(error.response.data.message);
          } else {
            setErrorMessage("Something went wrong, Please try again");
          }
        }
      } else if (error.request) {
        setErrorMessage("Network Error, Please check your internet connection");
      } else {
        setErrorMessage("Something went wrong, Please try again");
      }
    }
  };
  return (
    <View
      w="100%"
      bg={"white"}
      h={"100%"}
      px={moderateScale(24)}
      pt={moderateScale(24)}>
      <LoadingModal isLoading={loading} />
      <Box safeArea w="100%">
        <Toast
          visible={failed}
          position={30}
          shadow={true}
          animation={true}
          hideOnPress={true}
          backgroundColor={"red"}
          opacity={0.9}>
          {errorMessage}
        </Toast>

        <Heading
          fontSize={moderateScale(24)}
          mt={moderateScale(24)}
          fontFamily={"openSansSemiBold"}
          color="coolGray.800">
          Enter your phone number
        </Heading>
        <Heading
          mt={moderateScale(4)}
          fontFamily={"openSans"}
          color="coolGray.600"
          fontWeight="openSansMedium"
          fontSize={moderateScale(15)}>
          We will send a code (via SMS) to your phone number
        </Heading>

        <VStack space={moderateScale(16)} mt={moderateScale(48)}>
          <FormControl>
            <InputGroup>
              <InputLeftAddon
                roundedLeft={"lg"}
                w={scale(60)}
                children={
                  <Text
                    fontSize={moderateScale(15)}
                    fontFamily={"openSansMedium"}
                    color={"gray.600"}>
                    {"+263"}
                  </Text>
                }
              />
              <Input
                flex={1}
                fontFamily={"openSansSemiBold"}
                onFocus={() => {
                  setPhoneError("");
                }}
                focusOutlineColor={"#F68C1E"}
                borderColor={phoneError ? "red.400" : "gray.300"}
                placeholder="Netone Mobile Number"
                fontSize={moderateScale(15)}
                bgColor={"gray.100"}
                rounded={"lg"}
                onChangeText={(text) => setPhone(text)}
                h={verticalScale(48)}
                keyboardType={"number-pad"}
              />
            </InputGroup>
            <Text color={"red.400"} fontSize={moderateScale(15)}>
              {phoneError}
            </Text>
          </FormControl>

          <Button
            mt={moderateScale(24)}
            onPress={() => {
              sendPhoneNumber();
            }}
            h={verticalScale(48)}
            bg="#F68C1E"
            rounded={"lg"}>
            <Text
              fontSize={moderateScale(15)}
              color="white"
              fontFamily={"openSansSemiBold"}>
              Continue
            </Text>
          </Button>
        </VStack>
      </Box>
    </View>
  );
};

export default PhoneNumber;
