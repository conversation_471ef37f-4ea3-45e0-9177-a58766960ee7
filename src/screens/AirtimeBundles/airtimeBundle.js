import { <PERSON><PERSON><PERSON>, Octico<PERSON>, Feather } from "@expo/vector-icons";
import { useNavigation } from "@react-navigation/native";
import {
  Badge,
  Box,
  Button,
  Center,
  FlatList,
  FormControl,
  HStack,
  Input,
  InputGroup,
  InputLeftAddon,
  ScrollView,
  Select,
  Text,
  VStack,
  View,
} from "native-base";
import React, { useEffect, useRef, useState } from "react";
import {
  Dimensions,
  PermissionsAndroid,
  Platform,
  SafeAreaView,
  StyleSheet,
  TouchableOpacity,
} from "react-native";
import ActionSheet from "react-native-actions-sheet";
import * as Cont from "expo-contacts";
import Toast from "react-native-root-toast";
import { moderateScale, scale, verticalScale } from "react-native-size-matters";
import LoadingModal from "../../components/Loading/LoadingModal";
import GetContacts from "../../components/Modals/contacts";
import { FlashList } from "@shopify/flash-list";
import airtimeBundles from "../../services/Bundles/Bundles";
import BundleFetchFailed from "../../components/Modals/BundleFetchFailed";
import store from "../../utilities/store";

const AirtimeBundle = ({ route }) => {
  const [amount, setAmount] = useState();
  const [number, setNumber] = useState("");
  const [errorNumber, setErrorNumber] = useState();
  const [loading, setLoading] = useState(false);
  const [failed, setFailed] = useState(false);
  const height = Dimensions.get("window").height;
  const navigation = useNavigation();
  const [isYesSelected, setIsYesSelected] = useState(true);
  const [isNoSelected, setIsNoSelected] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const [openContacts, setOpenContacts] = useState(false);
  const [contacts, setContacts] = useState([]);
  const [searchText, setSearchText] = useState("");
  const [loadingContacts, setLoadingContacts] = useState(false);
  const [errorBundle, setErrorBundle] = useState(false);
  const [bundles, setBundles] = useState([]);
  const [BundleType, setBundleType] = useState("");
  const [BundleTypeError, setBundleTypeError] = useState("");
  const [selectedBundle, setSelectedBundle] = useState();
  const [bundleClasses, setBundleClasses] = useState([]);
  const [failedtofetch, setFailedToFetch] = useState(false);

  const actionSheetRef = useRef(null);
  const actionSheetRef2 = useRef(null);

  const validate = () => {
    const numberRegexp = /^(071|71)/;

    if (!BundleType) {
      setBundleTypeError("Please select a bundleType");
      return false;
    }

    //check mobile number
    if (!number && isNoSelected) {
      setErrorNumber("This field is required");
      return false;
    }

    if (!numberRegexp.test(number) && isNoSelected) {
      setErrorNumber("Invalid phone number, should be a netone number");
      return false;
    }

    if (number.length < 9 && isNoSelected) {
      setErrorNumber("Invalid phone number, not enough digits");
      return false;
    }

    if (number.length > 10 && isNoSelected) {
      setErrorNumber("Invalid phone number, too many digits");
      return false;
    }

    if (!selectedBundle) {
      setErrorBundle("Please select a bundle");
      return false;
    }

    return true;
  };

  useEffect(() => {
    const interval = setTimeout(() => {
      setFailed(false);
    }, 5000);

    return () => clearInterval(interval);
  }, [failed]);

  const handleYesPress = () => {
    setIsYesSelected(true);
    setIsNoSelected(false);
  };

  const handleNoPress = () => {
    setIsNoSelected(true);
    setIsYesSelected(false);
  };

  useEffect(() => {
    (async () => {
      const bundles = await store.getData("@bundles");

      if (bundles !== null) {
        setBundles(bundles);
        const bundleClasses = new Set();
        bundles.forEach((bundle) => {
          if (bundle.active) {
            bundleClasses.add(bundle.bundleClass);
          }
        });
        const uniqueBundleClasses = Array.from(bundleClasses);
        setBundleClasses(uniqueBundleClasses);
      } else {
        handleFetchBundles();
      }
    })();
  }, []);

  const handleLookUp = () => {
    const res = validate();
    if (!res) {
      return;
    }

    const allData = {
      buyerPhoneNumber: route.params.phone,
      receiverPhoneNumber: isYesSelected
        ? route.params.phone
        : "263" + parseInt(number),
      amount: amount,
      bundle: selectedBundle,
      other: isNoSelected,
    };

    navigation.navigate("confirmAirtimeBundle", {
      data: allData,
    });
  };

  const handleFetchBundles = async () => {
    try {
      setLoading(true);
      // useLayoutEffect
      const response = await airtimeBundles.GetBundles();
      if (response.data.success === true) {
        //set bundles
        setBundles(response.data.body);

        //save in state
        store.saveData("@bundles", response.data.body);
        // Create an empty set to store unique bundle classes
        const bundleClasses = new Set();
        // Loop through the bundles and add the bundleClass to the set if active is true
        response.data.body.forEach((bundle) => {
          if (bundle.active) {
            bundleClasses.add(bundle.bundleClass);
          }
        });

        // Convert the set to an array if needed
        const uniqueBundleClasses = Array.from(bundleClasses);
        setBundleClasses(uniqueBundleClasses);
      } else {
        setFailed(true);
        setFailedToFetch(true);
        setErrorMessage("Failed to fetch bundles");
      }

      setLoading(false);
    } catch (error) {
      console.log(error);

      setFailed(true);
      setFailedToFetch(true);

      setLoading(false);
      if (error.response.data) {
        if (error.response.status == 400) {
          setErrorMessage("Something went wrong, Please try again");
        } else if (
          error.response.status == 403 ||
          error.response.status == 500 ||
          error.response.status == 504
        ) {
          setErrorMessage("Server Error, Please try again later");
        } else {
          if (error.response.data.message) {
            setErrorMessage(error.response.data.message);
          } else {
            setErrorMessage("Something went wrong, Please try again");
          }
        }
      } else if (error.request) {
        setErrorMessage("Network Error, Please check your internet connection");
      } else {
        setErrorMessage("Something went wrong, Please try again");
      }
    }
  };

  const filterData = (data) => {
    return data.filter((item) =>
      item?.givenName?.toLowerCase().includes(searchText.toLowerCase())
    );
  };

  useEffect(() => {
    (async () => {
      const { status } = await Cont.requestPermissionsAsync();
      if (status === "granted") {
        const { data } = await Cont.getContactsAsync();

        if (data.length > 0) {
          setContacts(data);
        }
      }
    })();
  }, []);

  const filterBundles = () => {
    return bundles.filter((item) => {
      if (item.active === true) {
        return item.bundleClass
          .toLowerCase()
          .includes(BundleType.toLowerCase());
      }
    });
  };

  const renderRow = (item) => {
    return (
      <View key={item.index} minHeight={verticalScale(120)}>
        <View
          style={styles.MainContainer}
          bgColor={"white"}
          mb={moderateScale(15)}
          rounded={"xl"}
          shadow={1}>
          <TouchableOpacity
            onPress={() => {
              setSelectedBundle(item);
              setErrorBundle(false);
              setAmount(item.usdAmount);
              actionSheetRef2.current?.hide();
            }}>
            <View>
              <Badge
                bg="#F68C1E"
                colorScheme="danger"
                mb={-5}
                ml={0}
                zIndex={1}
                variant="solid"
                alignSelf="flex-start"
                _text={{
                  fontSize: 12,
                }}>
                <HStack>
                  <Text
                    fontSize={moderateScale(10)}
                    color="white"
                    fontFamily={"openSansMedium"}>
                    {item.bundleClass}{" "}
                  </Text>
                  <Text
                    fontSize={moderateScale(10)}
                    color="white"
                    fontFamily={"openSansMedium"}>
                    Bundle
                  </Text>
                </HStack>
              </Badge>
              <HStack
                h={"100%"}
                mt={moderateScale(2)}
                px={moderateScale(10)}
                alignContent={"center"}
                alignItems={"center"}>
                <View style={{ justifyContent: "center" }}>
                  <Text
                    fontFamily={"openSansMedium"}
                    color={"#36454F"}
                    fontWeight="500"
                    fontSize={moderateScale(14)}>
                    {`${item.displayText}`}{" "}
                  </Text>
                  <Text
                    fontFamily={"openSansMedium"}
                    color={"#36454F"}
                    fontWeight="500"
                    fontSize={moderateScale(14)}>
                    Amount: USD {item.usdAmount}{" "}
                  </Text>
                </View>
              </HStack>
            </View>
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  return (
    <SafeAreaView>
      <ScrollView showsVerticalScrollIndicator={false}>
        {failedtofetch && loading == false && (
          <View p={moderateScale(50)}>
            <BundleFetchFailed fetchBundles={handleFetchBundles} />
          </View>
        )}
        <View w="100%" bg={"white"} h={height}>
          <Box px={moderateScale(20)} py={moderateScale(10)} w="100%">
            <LoadingModal isLoading={loading} />

            <Toast
              visible={failed}
              position={70}
              shadow={true}
              animation={true}
              hideOnPress={true}
              backgroundColor={"red"}
              opacity={0.9}
              duration={Toast.durations.LONG}>
              {errorMessage}
            </Toast>

            <VStack space={moderateScale(16)} mt={moderateScale(48)}>
              <VStack space={4}>
                <FormControl mb={moderateScale(16)}>
                  <FormControl.Label>
                    <Text
                      fontSize={moderateScale(15)}
                      my={moderateScale(4)}
                      fontFamily={"openSansSemiBold"}>
                      Buy For:{" "}
                    </Text>
                  </FormControl.Label>
                  <HStack space={4}>
                    <TouchableOpacity
                      onPress={handleYesPress}
                      style={{ width: "40%", flex: 1 }}>
                      <View
                        h={verticalScale(48)}
                        rounded="lg"
                        borderColor="gray.300"
                        style={{
                          width: "100%",
                          borderWidth: 0,
                          alignItems: "center",
                          justifyContent: "center",
                        }}>
                        {isYesSelected ? (
                          <View
                            rounded="lg"
                            style={{
                              width: "100%",
                              height: "100%",
                              backgroundColor: "#F68C1E",
                              alignItems: "center",
                              justifyContent: "center",
                            }}>
                            <Text
                              color={"white"}
                              fontFamily={"openSansMedium"}
                              fontSize={moderateScale(15)}>
                              Self
                            </Text>
                          </View>
                        ) : (
                          <View
                            rounded="lg"
                            bgColor={"gray.100"}
                            style={{
                              width: "100%",
                              height: "100%",
                              alignItems: "center",
                              justifyContent: "center",
                            }}>
                            <Text
                              color={"black"}
                              fontFamily={"openSansMedium"}
                              fontSize={moderateScale(15)}>
                              Self
                            </Text>
                          </View>
                        )}
                      </View>
                    </TouchableOpacity>
                    <TouchableOpacity
                      onPress={handleNoPress}
                      style={{ width: "40%", flex: 1 }}>
                      <View
                        h={verticalScale(48)}
                        rounded="lg"
                        borderColor="gray.300"
                        style={{
                          width: "100%",
                          borderWidth: 0,

                          alignItems: "center",
                          justifyContent: "center",
                        }}>
                        {isNoSelected ? (
                          <View
                            rounded="lg"
                            style={{
                              width: "100%",
                              height: "100%",
                              backgroundColor: "#F68C1E",
                              alignItems: "center",
                              justifyContent: "center",
                            }}>
                            <Text
                              color={"white"}
                              fontFamily={"openSansMedium"}
                              fontSize={moderateScale(15)}>
                              Other
                            </Text>
                          </View>
                        ) : (
                          <View
                            rounded="lg"
                            bgColor={"gray.100"}
                            style={{
                              width: "100%",
                              height: "100%",
                              alignItems: "center",
                              justifyContent: "center",
                            }}>
                            <Text
                              color={"black"}
                              fontFamily={"openSansMedium"}
                              fontSize={moderateScale(15)}>
                              Other
                            </Text>
                          </View>
                        )}
                      </View>
                    </TouchableOpacity>
                  </HStack>
                </FormControl>

                {isNoSelected && (
                  <FormControl>
                    <InputGroup>
                      <InputLeftAddon
                        roundedLeft={"lg"}
                        w={scale(60)}
                        children={
                          <Text
                            fontSize={moderateScale(15)}
                            fontFamily={"openSansMedium"}
                            color={"gray.600"}>
                            {"+263"}
                          </Text>
                        }
                      />
                      <Input
                        flex={1}
                        defaultValue={`${number}`}
                        fontFamily={"openSansSemiBold"}
                        onFocus={() => {
                          setErrorNumber("");
                        }}
                        focusOutlineColor={"#F68C1E"}
                        borderColor={errorNumber ? "red.400" : "gray.300"}
                        placeholder="Receivers Netone Mobile Number"
                        fontSize={moderateScale(15)}
                        bgColor={"gray.100"}
                        rounded={"lg"}
                        onChangeText={(text) => setNumber(text)}
                        h={verticalScale(48)}
                        keyboardType={"number-pad"}
                      />
                    </InputGroup>
                    <TouchableOpacity
                      onPress={() => {
                        actionSheetRef.current?.show();
                      }}>
                      <Text color={"blue.400"} fontSize={moderateScale(15)}>
                        Select From Contacts
                      </Text>
                    </TouchableOpacity>
                    <Text color={"red.400"} fontSize={moderateScale(15)}>
                      {errorNumber}
                    </Text>
                  </FormControl>
                )}

                <Select
                  selectedValue={BundleType}
                  onOpen={() => {
                    setBundleTypeError("");
                  }}
                  bgColor={"gray.100"}
                  rounded={"lg"}
                  fontSize={moderateScale(15)}
                  fontFamily={"openSansMedium"}
                  h={verticalScale(48)}
                  onFocus={() => {
                    setBundleTypeError("");
                  }}
                  focusOutlineColor={"#F68C1E"}
                  borderColor={BundleTypeError ? "red.400" : "gray.300"}
                  accessibilityLabel="Banks"
                  placeholder="Select Bundle Type"
                  _selectedItem={{
                    bg: "teal.600",
                  }}
                  onValueChange={(itemValue) => setBundleType(itemValue)}>
                  {bundleClasses.map((item) => (
                    <Select.Item key={item} label={item} value={item} />
                  ))}
                </Select>
                <Text color={"red.400"} fontSize={moderateScale(15)}>
                  {BundleTypeError}
                </Text>

                {BundleType && (
                  <TouchableOpacity
                    onPress={() => {
                      actionSheetRef2.current?.show();
                    }}>
                    {selectedBundle ? (
                      <HStack
                        space={4}
                        px={moderateScale(13)}
                        display={"flex"}
                        justifyContent={"center"}
                        alignContent={"center"}
                        alignItems={"center"}
                        borderColor={errorBundle ? "red.400" : "gray.300"}
                        borderWidth={1}
                        fontSize={moderateScale(15)}
                        bgColor={"gray.100"}
                        rounded={"lg"}
                        h={verticalScale(48)}>
                        <Text
                          fontSize={moderateScale(15)}
                          color={"gray.400"}
                          fontFamily={"openSansMedium"}>
                          {selectedBundle.name}
                        </Text>
                      </HStack>
                    ) : (
                      <HStack
                        space={4}
                        px={moderateScale(13)}
                        display={"flex"}
                        justifyContent={"space-between"}
                        alignContent={"center"}
                        alignItems={"center"}
                        borderColor={errorBundle ? "red.400" : "gray.300"}
                        borderWidth={1}
                        fontSize={moderateScale(15)}
                        bgColor={"gray.100"}
                        rounded={"lg"}
                        h={verticalScale(48)}>
                        <Text
                          fontSize={moderateScale(15)}
                          color={"gray.400"}
                          fontFamily={"openSansMedium"}>
                          Select Bundle Plan
                        </Text>
                        <Octicons
                          name="single-select"
                          size={moderateScale(24)}
                          color="gray"
                        />
                      </HStack>
                    )}
                    <Text color={"red.400"} fontSize={moderateScale(15)}>
                      {errorBundle}
                    </Text>
                  </TouchableOpacity>
                )}
                {selectedBundle && (
                  <FormControl>
                    <View>
                      <Text
                        fontSize={moderateScale(15)}
                        color="black"
                        fontFamily={"openSansSemiBold"}>
                        Selected Bundle Amount :USD {selectedBundle.usdAmount}
                      </Text>
                    </View>
                  </FormControl>
                )}
              </VStack>

              <Button
                onPressIn={() => {}}
                mt={moderateScale(24)}
                onPress={() => handleLookUp()}
                h={verticalScale(48)}
                bg="#F68C1E"
                rounded={"lg"}>
                <Text
                  fontSize={moderateScale(15)}
                  color="white"
                  fontFamily={"openSansSemiBold"}>
                  Continue
                </Text>
              </Button>

              {openContacts && (
                <GetContacts
                  open={openContacts}
                  setNumber={(value) => {
                    setNumber(value);
                  }}
                  close={() => {
                    setOpenContacts(false);
                  }}></GetContacts>
              )}

              <ActionSheet
                ref={actionSheetRef}
                containerStyle={{
                  borderTopLeftRadius: 25,
                  borderTopRightRadius: 25,
                }}
                indicatorStyle={{
                  width: 100,
                }}
                gestureEnabled={false}>
                <HStack p={moderateScale(10)}>
                  <TouchableOpacity
                    onPress={() => {
                      actionSheetRef.current?.hide();
                    }}>
                    <Feather name="x" size={moderateScale(24)} color="gray" />
                  </TouchableOpacity>
                </HStack>
                <View
                  w="100%"
                  h={"88%"}
                  px={moderateScale(16)}
                  pt={moderateScale(4)}>
                  <Input
                    fontFamily={"openSansSemiBold"}
                    my={moderateScale(20)}
                    value={searchText}
                    focusOutlineColor={"#F68C1E"}
                    borderColor={"gray.300"}
                    placeholder="Search Contact"
                    fontSize={moderateScale(15)}
                    bgColor={"gray.100"}
                    rounded={"lg"}
                    onChangeText={(text) => setSearchText(text)}
                    h={verticalScale(48)}
                  />
                  {loadingContacts && (
                    <Center>
                      <Text color={"blue.500"}>Getting Contacts ...</Text>
                    </Center>
                  )}
                  {filterData(contacts).length == 0 && !loadingContacts && (
                    <Center>
                      <Text
                        color={"red.500"}
                        fontFamily={"openSansMedium"}
                        fontWeight="500"
                        fontSize={moderateScale(16)}
                        my={moderateScale(15)}>
                        No Contacts Found
                      </Text>
                    </Center>
                  )}
                  {filterData(contacts).length > 0 && !loadingContacts && (
                    <Text
                      color={"gray.500"}
                      fontFamily={"openSansMedium"}
                      fontWeight="500"
                      fontSize={moderateScale(16)}
                      my={moderateScale(15)}>
                      Tap on a contact to select
                    </Text>
                  )}
                  {filterData(contacts).length > 0 && !loadingContacts && (
                    <Animatable.View
                      easing="ease-out"
                      animation={"fadeInUp"}
                      delay={300}>
                      <View height={"100%"} width={"100%"}>
                        <FlashList
                          data={filterData(contacts)}
                          estimatedItemSize={200}
                          showsVerticalScrollIndicator={false}
                          contentContainerStyle={{
                            paddingBottom: moderateScale(200),
                          }}
                          renderItem={({ item }) => {
                            if (
                              item.contactType === "person" &&
                              item.phoneNumbers
                            ) {
                              return (
                                <View key={item} h={verticalScale(70)}>
                                  <View
                                    style={styles.MainContainer}
                                    bgColor={"white"}
                                    mb={moderateScale(15)}
                                    rounded={"xl"}
                                    shadow={0.5}
                                    borderColor={"gray.200"}
                                    borderWidth={1}>
                                    <TouchableOpacity
                                      onPress={() => {
                                        setNumber(
                                          item.phoneNumbers?.[0].number
                                            .toString()
                                            .replace(/\s/g, "")
                                            .replace("+263", "")
                                        );
                                        actionSheetRef.current?.hide();
                                      }}>
                                      <Center h={"100%"}>
                                        <Text style={{ fontSize: 18 }}>
                                          {" "}
                                          {`${item && item?.name}`} -{" "}
                                          {`${
                                            item &&
                                            item?.phoneNumbers[0]?.number
                                          }`}
                                        </Text>
                                      </Center>
                                    </TouchableOpacity>
                                  </View>
                                </View>
                              );
                            }
                          }}
                          numColumns={1}
                        />
                      </View>
                    </Animatable.View>
                  )}
                </View>
              </ActionSheet>
              <ActionSheet ref={actionSheetRef2}>
                <HStack p={moderateScale(10)}>
                  <TouchableOpacity
                    onPress={() => {
                      actionSheetRef2.current?.hide();
                    }}>
                    <Entypo
                      name="cross"
                      size={moderateScale(24)}
                      color="gray"
                    />
                  </TouchableOpacity>
                </HStack>
                <View
                  w="100%"
                  h={"85%"}
                  px={moderateScale(16)}
                  pt={moderateScale(4)}
                  pb={moderateScale(0)}>
                  <FlatList
                    data={filterBundles()}
                    renderItem={({ item }) => renderRow(item)}
                    numColumns={1}
                    showsVerticalScrollIndicator={false}
                    contentContainerStyle={{
                      paddingBottom: moderateScale(100),
                      paddingHorizontal: moderateScale(4),
                      paddingTop: moderateScale(8),
                    }}
                    ListEmptyComponent={() => {
                      return (
                        <>
                          <Center h={"100%"}>
                            <Text style={{ fontSize: 18 }}>
                              {" "}
                              No Bundles Found{" "}
                            </Text>
                          </Center>
                        </>
                      );
                    }}
                  />
                </View>
              </ActionSheet>
            </VStack>
          </Box>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  MainContainer: {
    flex: 1,
    paddingTop: Platform.OS === "ios" ? 20 : 0,
  },

  title: {
    padding: 12,
    fontSize: 22,
    backgroundColor: "#33691E",
    color: "white",
  },

  contactTitle: {
    fontSize: 22,
    paddingTop: 8,
    paddingBottom: 8,
    paddingLeft: 15,
    color: "black",
  },

  row: {
    flexDirection: "row",
    height: 60,
  },

  avatarContainer: {
    marginLeft: 12,
    justifyContent: "center",
    alignItems: "center",
  },

  listTextContainer: {
    marginLeft: 15,
    flexDirection: "row",
    flex: 1,
  },
});

export default AirtimeBundle;
