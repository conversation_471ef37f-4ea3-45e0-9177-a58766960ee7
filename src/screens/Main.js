import {
  MaterialCommunityIcons,
  SimpleLineIcons,
  Ionicons,
} from "@expo/vector-icons";
import { Link, useNavigation } from "@react-navigation/native";
import {
  AlertDialog,
  Box,
  Button,
  Center,
  HStack,
  Heading,
  Image,
  ScrollView,
  Spinner,
  Stack,
  StatusBar,
  Text,
  VStack,
  View,
  Badge,
} from "native-base";
import React, { useEffect, useLayoutEffect, useRef, useState } from "react";
import {
  FlatList,
  ImageBackground,
  Linking,
  Platform,
  SafeAreaView,
  StyleSheet,
  TouchableOpacity,
  RefreshControl,
  Dimensions,
} from "react-native";
import Toast from "react-native-root-toast";
import { moderateScale, scale, verticalScale } from "react-native-size-matters";
import Card from "../assets/Card.png";
import AppbarMain from "../components/Headers/AppbarMain";
import LoadingModal from "../components/Loading/LoadingModal";
import { greeting } from "../utilities/greeting";
import LocalStore from "../utilities/store";
import Balance from "../services/Balance/Balance";
import ActionSheet from "react-native-actions-sheet";
import SessionTimeOut from "../components/Modals/sessionTimeOut";
import { useAuth } from "../utilities/AuthContext";

const Main4 = () => {
  const navigation = useNavigation();
  const { logout: authLogout, userInfo } = useAuth();
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [Phone, setPhone] = useState(null);
  const [init, setInit] = useState("");
  const [failed, setFailed] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const [isOpen, setIsOpen] = useState(false);
  const cancelRef = useRef(null);
  const onClose = () => setIsOpen(false);
  const [balance, setBalance] = useState([]);
  const actionSheetRef = useRef(null);
  const [loadingSend, setLoadingSend] = useState(false);
  const [loadingBalance, setLoadingBalance] = useState(false);
  const [usd, setUsd] = useState(0.0);
  const [ZWG, setZWG] = useState(0.0);
  const [isPostpaid, setIsPostpaid] = useState(false);
  const [postpaidData, setPostpaidData] = useState(null);
  const [subscriberProfile, setSubscriberProfile] = useState(null);

  const { width } = Dimensions.get("window");

  const logout = async () => {
    setLoading(true);
    const success = await authLogout();
    if (success) {
      navigation.navigate("Landing");
    }
    setLoading(false);
  };

  useEffect(() => {
    const interval = setTimeout(() => {
      setFailed(false);
    }, 5000);

    return () => clearInterval(interval);
  }, [failed]);

  useEffect(() => {
    const getProfile = async () => {
      const Phone = await LocalStore.getData("@username");
      //check if phone number is stored
      if (Phone !== null) {
        setPhone(Phone);
        checkSubscriberType(Phone);
      }
    };
    getProfile();
  }, []);

  const checkSubscriberType = async (phone) => {
    try {
      const response = await Balance.getSubscriberProfile("263" + phone);
      if (response.data.success) {
        const profile = response.data.body;
        setSubscriberProfile(profile);
        const isPostpaidUser = profile.postPaid === "1";
        setIsPostpaid(isPostpaidUser);

        // Load balance based on subscriber type
        if (isPostpaidUser) {
          handlePostpaidBalanceEnquiry(phone, true);
        } else {
          handleBalanceEnquiry(false, phone, true);
        }
      }
    } catch (error) {
      console.log("Error checking subscriber type:", error);
      // Fallback to prepaid if error
      setIsPostpaid(false);
      handleBalanceEnquiry(false, phone, true);
    }
  };

  const handlePostpaidBalanceEnquiry = async (phone, noloading = false) => {
    try {
      !noloading && setLoading(true);
      setFailed(false);

      const response = await Balance.getPostpaidBalance("263" + phone);

      if (response.data.success) {
        setPostpaidData(response.data.body);
      } else {
        setFailed(true);
        setErrorMessage(response.data.message);
      }
      setLoading(false);
    } catch (error) {
      setLoading(false);
      setFailed(true);
      console.log(error);
      handleError(error);
    }
  };

  const handleError = (error) => {
    if (error.response) {
      if (error.response.status === 400) {
        setErrorMessage("Something went wrong, Please try again");
      } else if (
        error.response.status === 403 ||
        error.response.status === 500 ||
        error.response.status === 504
      ) {
        setErrorMessage("Server Error, Please try again later");
      } else {
        if (error.response.data.message) {
          setErrorMessage(error.response.data.message);
        } else {
          setErrorMessage("Something went wrong, Please try again");
        }
      }
    } else if (error.request) {
      setErrorMessage("Network Error, Please check your internet connection");
    } else {
      setErrorMessage("Something went wrong, Please try again");
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    if (isPostpaid) {
      await handlePostpaidBalanceEnquiry(Phone);
    } else {
      await handleBalanceEnquiry(false, Phone);
    }
    setRefreshing(false);
  };

  const dataItems = [
    {
      key: "1",
      icon: "currency-usd",
      name: "airtimeBundle",
      displayName: "Buy USD Bundles",
      data: {
        phone: Phone,
      },
    },

    {
      key: "2",
      icon: "credit-card-sync",
      name: "airtimePurchase",
      displayName: "Airtime Recharge",
      data: {
        phone: Phone,
      },
    },

    {
      key: "3",
      icon: "arrow-vertical-lock",
      name: "requestPuk",
      displayName: "Request Puk",
      data: {
        phone: Phone,
      },
    },
    {
      key: "3",
      icon: "dialpad",
      name: "ussdCodes",
      displayName: "USSD Codes",
      data: {
        phone: Phone,
      },
    },
    {
      key: "3",
      icon: "credit-card-chip",
      name: "airtimeBundle",
      displayName: "One Money",
      link: true,
      url: "https://play.google.com/store/apps/details?id=com.emacliam.OneMoney",
      data: {
        phone: Phone,
      },
    },
    {
      key: "3",
      icon: "shopping",
      name: "shops",
      displayName: "Netone Shops",
      data: {
        phone: Phone,
      },
    },
  ];

  useLayoutEffect(() => {
    (async function getBalance() {
      const ZWG = await LocalStore.getData("@ZWG");
      const usd = await LocalStore.getData("@usd");

      if (ZWG !== null) {
        setZWG(ZWG);
      }

      if (usd !== null) {
        setUsd(usd);
      }
    })();
  }, []);

  const handleBalanceEnquiry = async (viewAll, Phone, noloading) => {
    try {
      noloading ? "" : setLoading(true);
      setFailed(false);

      const response = await Balance.getBalance("263" + Phone);
      console.log(response.data);

      if (response.data.success) {
        setLoading(false);

        if (viewAll) {
          const filteredData = [];
          const remainingData = [];

          //combine bundles
          const combinedData = Object.values(
            response.data.body.reduce((acc, item) => {
              if (!acc[item.acctResId]) {
                acc[item.acctResId] = { ...item };
              } else {
                acc[item.acctResId].balance += item.balance;
              }
              return acc;
            }, {})
          );

          combinedData.forEach((item) => {
            if (item.accountResName.includes("Data")) {
              filteredData.push({
                ...item,
                balance: `${Math.abs(item.balance / (1024 * 1024)).toFixed(
                  2
                )} MB`,
              });
            } else if (item.accountResName.includes("Voice")) {
              filteredData.push({
                ...item,
                balance: `${Math.abs(item.balance / 60)} minutes`,
              });
            } else {
              remainingData.push({
                ...item,
                balance: Math.abs(item.balance.toFixed(3)),
              });
            }
          });

          const usd = remainingData.find(
            (item) => item.accountResName === "USD Currency Balance"
          );
          const ZWG = remainingData.find(
            (item) => item.accountResName === "ZWG Currency"
          );
          const others = remainingData.filter(
            (item) =>
              item.accountResName !== "USD Currency Balance" &&
              item.accountResName !== "ZWG Currency"
          );

          setBalance([usd, ZWG, ...filteredData, ...others]);

          setTimeout(() => {
            actionSheetRef.current.show();
          }, 500);
        } else {
          setUsd(0.0);
          LocalStore.saveData("@usd", 0.0);
          response.data.body.forEach((element) => {
            if (element.accountResName === "ZWG Currency") {
              setZWG(element.balance);
              LocalStore.saveData("@ZWG", element.balance);
            }

            if (element.accountResName === "USD Currency Balance") {
              setUsd(element.balance);
              LocalStore.saveData("@usd", element.balance);
            }
          });
        }
      } else {
        setFailed(true);
        setErrorMessage(response.data.message);
      }
      setLoading(false);
    } catch (error) {
      setLoading(false);
      setFailed(true);
      console.log(error);
      if (error.response) {
        if (error.response.status == 400) {
          setErrorMessage("Something went wrong, Please try again");
        } else if (
          error.response.status == 403 ||
          error.response.status == 500 ||
          error.response.status == 504
        ) {
          setErrorMessage("Server Error, Please try again later");
        } else {
          if (error.response.data.message) {
            setErrorMessage(error.response.data.message);
          } else {
            setErrorMessage("Something went wrong, Please try again");
          }
        }
      } else if (error.request) {
        setErrorMessage("Network Error, Please check your internet connection");
      } else {
        setErrorMessage("Something went wrong, Please try again");
      }
    }
  };

  const BalanceCard = () => {
    if (isPostpaid && postpaidData) {
      return (
        <Box
          rounded={"3xl"}
          w={"full"}
          shadow={"9"}
          overflow="hidden"
          bg={{
            linearGradient: {
              colors: ["#667eea", "#764ba2"],
              start: [0, 0],
              end: [1, 1],
            },
          }}
          h={moderateScale(220)}>
          <VStack
            h={"full"}
            py={moderateScale(24)}
            px={moderateScale(20)}
            justifyContent={"space-between"}>
            {/* Header */}
            <HStack justifyContent={"space-between"} alignItems={"center"}>
              <VStack>
                <Text
                  fontFamily={"openSansMedium"}
                  color={"white"}
                  opacity={0.9}
                  fontSize={moderateScale(14)}>
                  Postpaid Account
                </Text>
                <Text
                  fontFamily={"openSansBold"}
                  color={"white"}
                  fontSize={moderateScale(18)}>
                  {userInfo?.firstName || "User"}
                </Text>
              </VStack>
              <Badge
                bg={"rgba(255,255,255,0.2)"}
                rounded={"full"}
                px={moderateScale(12)}
                py={moderateScale(4)}>
                <Text
                  fontFamily={"openSansSemiBold"}
                  color={"white"}
                  fontSize={moderateScale(12)}>
                  POSTPAID
                </Text>
              </Badge>
            </HStack>

            {/* Balance Info */}
            <VStack space={moderateScale(12)}>
              <VStack>
                <Text
                  fontFamily={"openSansMedium"}
                  color={"white"}
                  opacity={0.9}
                  fontSize={moderateScale(14)}>
                  Credit Limit Remaining
                </Text>
                <Text
                  fontFamily={"openSansBold"}
                  color={"white"}
                  fontSize={moderateScale(32)}>
                  ${Math.abs(postpaidData.creditLimitRemaining).toFixed(2)}
                </Text>
              </VStack>

              <HStack justifyContent={"space-between"}>
                <VStack alignItems={"flex-start"}>
                  <Text
                    fontFamily={"openSansMedium"}
                    color={"white"}
                    opacity={0.8}
                    fontSize={moderateScale(12)}>
                    Credit Limit
                  </Text>
                  <Text
                    fontFamily={"openSansSemiBold"}
                    color={"white"}
                    fontSize={moderateScale(16)}>
                    ${postpaidData.creditLimit.toFixed(2)}
                  </Text>
                </VStack>
                <VStack alignItems={"flex-end"}>
                  <Text
                    fontFamily={"openSansMedium"}
                    color={"white"}
                    opacity={0.8}
                    fontSize={moderateScale(12)}>
                    Used
                  </Text>
                  <Text
                    fontFamily={"openSansSemiBold"}
                    color={"white"}
                    fontSize={moderateScale(16)}>
                    ${postpaidData.creditsUsed.toFixed(2)}
                  </Text>
                </VStack>
              </HStack>
            </VStack>

            {/* Action Buttons */}
            <HStack justifyContent={"center"} space={moderateScale(16)}>
              <TouchableOpacity
                onPress={() => handlePostpaidBalanceEnquiry(Phone)}>
                <Box
                  bg={"rgba(255,255,255,0.2)"}
                  rounded={"full"}
                  px={moderateScale(20)}
                  py={moderateScale(10)}>
                  <HStack space={2} alignItems={"center"}>
                    <Ionicons
                      name="refresh"
                      size={moderateScale(16)}
                      color="white"
                    />
                    <Text
                      fontFamily={"openSansSemiBold"}
                      fontSize={moderateScale(14)}
                      color={"white"}>
                      Refresh
                    </Text>
                  </HStack>
                </Box>
              </TouchableOpacity>
            </HStack>
          </VStack>
        </Box>
      );
    }

    // Prepaid Balance Card
    return (
      <Box
        rounded={"3xl"}
        w={"full"}
        shadow={"9"}
        overflow="hidden"
        bg={{
          linearGradient: {
            colors: ["#F68C1E", "#ff7f50"],
            start: [0, 0],
            end: [1, 1],
          },
        }}
        h={moderateScale(220)}>
        <VStack
          h={"full"}
          py={moderateScale(24)}
          px={moderateScale(20)}
          justifyContent={"space-between"}>
          {/* Header */}
          <HStack justifyContent={"space-between"} alignItems={"center"}>
            <VStack>
              <Text
                fontFamily={"openSansMedium"}
                color={"white"}
                opacity={0.9}
                fontSize={moderateScale(14)}>
                Prepaid Account
              </Text>
              <Text
                fontFamily={"openSansBold"}
                color={"white"}
                fontSize={moderateScale(18)}>
                {userInfo?.firstName || "User"}
              </Text>
            </VStack>
            <Badge
              bg={"rgba(255,255,255,0.2)"}
              rounded={"full"}
              px={moderateScale(12)}
              py={moderateScale(4)}>
              <Text
                fontFamily={"openSansSemiBold"}
                color={"white"}
                fontSize={moderateScale(12)}>
                PREPAID
              </Text>
            </Badge>
          </HStack>

          {/* Balance Info */}
          <VStack space={moderateScale(8)}>
            <Text
              fontFamily={"openSansMedium"}
              color={"white"}
              opacity={0.9}
              fontSize={moderateScale(14)}>
              Airtime Balance
            </Text>
            <HStack space={moderateScale(20)} justifyContent={"center"}>
              <VStack alignItems={"center"}>
                <Text
                  fontFamily={"openSansMedium"}
                  color={"white"}
                  opacity={0.8}
                  fontSize={moderateScale(12)}>
                  ZWG
                </Text>
                <Text
                  fontFamily={"openSansBold"}
                  color={"white"}
                  fontSize={moderateScale(28)}>
                  {Math.abs(ZWG).toFixed(1)}
                </Text>
              </VStack>
              <VStack alignItems={"center"}>
                <Text
                  fontFamily={"openSansMedium"}
                  color={"white"}
                  opacity={0.8}
                  fontSize={moderateScale(12)}>
                  USD
                </Text>
                <Text
                  fontFamily={"openSansBold"}
                  color={"white"}
                  fontSize={moderateScale(28)}>
                  {Math.abs(usd).toFixed(1)}
                </Text>
              </VStack>
            </HStack>
          </VStack>

          {/* Action Buttons */}
          <HStack justifyContent={"center"} space={moderateScale(16)}>
            <TouchableOpacity
              onPress={() => handleBalanceEnquiry(false, Phone)}>
              <Box
                bg={"rgba(255,255,255,0.2)"}
                rounded={"full"}
                px={moderateScale(20)}
                py={moderateScale(10)}>
                <HStack space={2} alignItems={"center"}>
                  <Ionicons
                    name="refresh"
                    size={moderateScale(16)}
                    color="white"
                  />
                  <Text
                    fontFamily={"openSansSemiBold"}
                    fontSize={moderateScale(14)}
                    color={"white"}>
                    Refresh
                  </Text>
                </HStack>
              </Box>
            </TouchableOpacity>
            <TouchableOpacity onPress={() => handleBalanceEnquiry(true, Phone)}>
              <Box
                bg={"rgba(255,255,255,0.2)"}
                rounded={"full"}
                px={moderateScale(20)}
                py={moderateScale(10)}>
                <HStack space={2} alignItems={"center"}>
                  <Ionicons name="eye" size={moderateScale(16)} color="white" />
                  <Text
                    fontFamily={"openSansSemiBold"}
                    fontSize={moderateScale(14)}
                    color={"white"}>
                    View More
                  </Text>
                </HStack>
              </Box>
            </TouchableOpacity>
          </HStack>
        </VStack>
      </Box>
    );
  };

  const GridComponent = () => {
    const renderItem = ({ item }) => (
      <View
        style={{
          flex: 1,
          height: verticalScale(110),
          margin: moderateScale(0),
        }}>
        <VStack alignItems={"center"}>
          <TouchableOpacity
            onPress={async () => {
              if (item.link) {
                await Linking.openURL(item.url);
              } else {
                navigation.navigate(item.name, item.data);
              }
            }}>
            <Box
              alignItems="center"
              borderWidth={1}
              p={moderateScale(10)}
              height={scale(60)}
              width={scale(60)}
              justifyContent={"center"}
              rounded="full"
              overflow="hidden"
              borderColor={"white"}
              bg={"#F68C1E"}>
              <MaterialCommunityIcons
                name={item.icon}
                size={moderateScale(30)}
                color="white"
              />
            </Box>
          </TouchableOpacity>
          <Text
            mt={moderateScale(4)}
            fontFamily={"openSansMedium"}
            color={"#36454F"}
            fontWeight="500"
            textAlign={"center"}
            fontSize={moderateScale(15)}>
            {item.displayName}
          </Text>
        </VStack>
      </View>
    );

    return (
      <FlatList
        data={dataItems}
        renderItem={renderItem}
        contentContainerStyle={{ rowGap: 10 }}
        numColumns={3}
        keyExtractor={(item) => item.key}
      />
    );
  };

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: "#F68C1E" }}>
      <StatusBar backgroundColor={"#F68C1E"} />
      <ScrollView
        flex={1}
        bg={"gray.50"}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }>
        <LoadingModal isLoading={loading} />

        {/* Header Section */}
        <Box bgColor={"#F68C1E"} w={"full"} px={moderateScale(10)}>
          <View bg={"transparent"}>
            {Platform.OS == "android" ? (
              <Box
                bg={"transparent"}
                px={moderateScale(0)}
                safeArea
                pb={moderateScale(0)}>
                <AppbarMain
                  greeting={greeting()}
                  name={userInfo?.firstName || ""}
                  phone={Phone}
                  initials={userInfo?.firstName?.charAt(0) || "U"}
                  logout={logout}
                />
              </Box>
            ) : (
              <Box
                px={moderateScale(0)}
                pb={moderateScale(0)}
                bg={"transparent"}>
                <AppbarMain
                  greeting={greeting()}
                  name={userInfo?.firstName || ""}
                  phone={Phone}
                  initials={userInfo?.firstName?.charAt(0) || "U"}
                  logout={logout}
                />
              </Box>
            )}
          </View>
        </Box>

        {/* Balance Card */}
        <View w={"full"} px={moderateScale(16)} py={moderateScale(20)}>
          <BalanceCard />
        </View>

        {/* Quick Actions Section */}
        <View
          bgColor={"white"}
          flex={1}
          w={"full"}
          pt={moderateScale(24)}
          roundedTop={"3xl"}>
          <Box px={moderateScale(20)} pt={moderateScale(20)}>
            <HStack
              justifyContent={"space-between"}
              mb={moderateScale(24)}
              alignContent={"center"}
              alignItems={"center"}>
              <VStack>
                <Text
                  fontFamily={"openSansBold"}
                  fontWeight="700"
                  fontSize={moderateScale(20)}
                  color={"gray.800"}>
                  Quick Actions
                </Text>
                <Text
                  fontFamily={"openSansMedium"}
                  fontSize={moderateScale(14)}
                  color={"gray.500"}>
                  Manage your account easily
                </Text>
              </VStack>
              <Box bg={"gray.100"} rounded={"full"} p={moderateScale(8)}>
                <MaterialCommunityIcons
                  name="lightning-bolt"
                  size={moderateScale(24)}
                  color="#F68C1E"
                />
              </Box>
            </HStack>

            <GridComponent />
          </Box>
        </View>

        {/* Action Sheet for Balance Details */}
        <ActionSheet ref={actionSheetRef} gestureEnabled={true}>
          <Box h={moderateScale(400)} p={moderateScale(20)}>
            <Text
              fontSize={moderateScale(18)}
              fontFamily={"openSansBold"}
              mb={moderateScale(16)}
              textAlign={"center"}>
              Balance Details
            </Text>
            <FlatList
              data={balance}
              keyExtractor={(item) => item.acctResId}
              renderItem={({ item }) => (
                <HStack
                  justifyContent={"space-between"}
                  py={moderateScale(12)}
                  borderBottomWidth={1}
                  borderBottomColor={"gray.200"}>
                  <Text
                    fontSize={moderateScale(14)}
                    fontFamily={"openSansMedium"}
                    flex={1}
                    color={"gray.700"}>
                    {item.accountResName}
                  </Text>
                  <Text
                    fontSize={moderateScale(14)}
                    fontFamily={"openSansBold"}
                    color={"gray.900"}>
                    {item.balance}
                  </Text>
                </HStack>
              )}
            />
          </Box>
        </ActionSheet>

        {/* Session Timeout Modal */}
        {/*         <SessionTimeOut isOpen={isOpen} onClose={onClose} cancelRef={cancelRef} /> */}
      </ScrollView>

      {/* Error Alert */}
      {failed && (
        <AlertDialog
          leastDestructiveRef={cancelRef}
          isOpen={failed}
          onClose={() => setFailed(false)}>
          <AlertDialog.Content>
            <AlertDialog.CloseButton />
            <AlertDialog.Header>Error</AlertDialog.Header>
            <AlertDialog.Body>
              <Text>{errorMessage}</Text>
            </AlertDialog.Body>
            <AlertDialog.Footer>
              <Button.Group space={2}>
                <Button
                  variant="unstyled"
                  colorScheme="coolGray"
                  onPress={() => setFailed(false)}
                  ref={cancelRef}>
                  Cancel
                </Button>
                <Button
                  colorScheme="danger"
                  onPress={() => {
                    setFailed(false);
                    if (isPostpaid) {
                      handlePostpaidBalanceEnquiry(Phone);
                    } else {
                      handleBalanceEnquiry(false, Phone);
                    }
                  }}>
                  Retry
                </Button>
              </Button.Group>
            </AlertDialog.Footer>
          </AlertDialog.Content>
        </AlertDialog>
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  itemContainer: {
    flexDirection: "row",
    alignItems: "center",
    padding: 10,
    borderWidth: 1,
    margin: 4,
    borderColor: "#ccc",
    borderRadius: 10,
  },
  itemName: {
    flex: 1,
  },
  container: {
    flex: 1,
    padding: 0,
  },
});

export default Main4;
