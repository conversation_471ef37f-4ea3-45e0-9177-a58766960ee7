import { FontAwesome5 } from "@expo/vector-icons";
import { useNavigation } from "@react-navigation/native";
import { Box, Center, HStack, Heading, Text, VStack, View } from "native-base";
import React, { useEffect, useRef, useState } from "react";
import { Dimensions, Platform, SafeAreaView } from "react-native";
import Toast from "react-native-root-toast";
import { moderateScale } from "react-native-size-matters";
import AppbarCancel from "../../components/Headers/AppbarCancel";
import LoadingModal from "../../components/Loading/LoadingModal";
import SendMoneySuccess from "../../components/Modals/sendMoneySuccess";
import { VirtualKeyboard } from "../../packages/VirtualKeyboard";
import AirtimeService from "../../services/Core/airtime";

const ConfirmAirtime = ({ route }) => {
  const height = Dimensions.get("window").height;
  const [loading, setLoading] = useState(false);
  const [failed, setFailed] = useState(false);
  const [userDetails, setUserDetails] = useState(route.params.userDetails[0]);
  const [dataDetails, setDataDetails] = useState(route.params.data);
  const [errorMessage, setErrorMessage] = useState("");
  const [success, setSuccess] = useState(false);
  const navigation = useNavigation();
  const pinRef = useRef(null);
  const goBack = () => {
    navigation.goBack();
  };
  const [pins, setPin] = useState("");

  useEffect(() => {
    const interval = setTimeout(() => {
      setFailed(false);
    }, 5000);
    return () => clearInterval(interval);
  }, [failed]);

  const handleBuyAirtime = async () => {
    setLoading(true);
    setFailed(false);
    try {
      const data = {
        buyerPhoneNumber: dataDetails.buyerPhoneNumber,
        receiverPhoneNumber: dataDetails.receiverPhoneNumber,
        currency: dataDetails.currency,
        amount: dataDetails.amount,
        airtimeCode: dataDetails.amount,
        pin: pinRef.current.state.text,
      };
      let response;
      console.log(data);
      if (dataDetails.currency === "ZWL") {
        response = await AirtimeService.buyBundle(data);
      } else {
        response = await AirtimeService.buyBundleUSD(data);
      }

      if (response.data.success) {
        navigation.navigate("SuccessSend", {
          data: {
            title: "Airtime Purchase",
            message: "Purchase was successful",
            amount: dataDetails.amount,
            currency: dataDetails.currency,
            beneficiaryPhoneNumber: dataDetails.receiverPhoneNumber,
            status: "individual",
            beneficiaryName:
              dataDetails.other === true
                ? ""
                : userDetails.field_1 +
                  " " +
                  userDetails.field_2 +
                  " " +
                  userDetails.field_3,
          },
        });
      } else {
        setFailed(true);
        if (
          response.data.message.startsWith(
            "Identifier not invalid or not exist"
          )
        ) {
          setErrorMessage("Sorry, Number does not exist");
        } else {
          console.log(response.data);
          setErrorMessage(response.data.message);
        }
      }
      setLoading(false);
      setPin("");
      pinRef.current.state.text = "";
    } catch (error) {
      setPin("");
      pinRef.current.state.text = "";
      setLoading(false);
      setFailed(true);
      console.log(pins);

      if (error.response) {
        if (error.response.status == 400) {
          setErrorMessage("Something went wrong, Please try again");
        } else if (
          error.response.status == 403 ||
          error.response.status == 500 ||
          error.response.status == 504
        ) {
          setErrorMessage("Server Error, Please try again later");
        } else {
          if (error.response.data.message) {
            setErrorMessage(error.response.data.message);
          } else {
            setErrorMessage("Something went wrong, Please try again");
          }
        }
      } else if (error.request) {
        setErrorMessage("Network Error, Please check your internet connection");
      } else {
        setErrorMessage("Something went wrong, Please try again");
      }
    }
  };
  return (
    <SafeAreaView>
      <View
        bg={"white"}
        style={{
          display: "flex",
          flexDirection: "column",
          justifyContent: "space-between",
          height: "100%",
        }}>
        <View px={moderateScale(20)} py={moderateScale(10)}>
          <LoadingModal isLoading={loading} />
          <Toast
            visible={failed}
            position={70}
            shadow={true}
            animation={true}
            hideOnPress={true}
            backgroundColor={"red"}
            opacity={0.9}
            duration={Toast.durations.LONG}>
            {errorMessage}
          </Toast>
          {success && <SendMoneySuccess></SendMoneySuccess>}
          <Center>
            <HStack
              mb={5}
              alignItems={"center"}
              space={moderateScale(6)}
              mt={moderateScale(50)}>
              <Text
                fontFamily={"openSansBold"}
                fontSize={moderateScale(15)}
                color={"#252622"}
                fontWeight="500">
                Purchasing Airtime For:
              </Text>
            </HStack>
            <VStack justifyContent={"center"} alignItems={"center"}>
              {userDetails ? (
                <>
                  <Heading
                    fontFamily={"openSans"}
                    mt={moderateScale(6)}
                    fontSize={moderateScale(15)}
                    color={"#252622"}
                    fontWeight="500">
                    {userDetails.field_1} {userDetails.field_2}{" "}
                    {userDetails.field_3}
                  </Heading>
                  <Heading
                    fontFamily={"openSans"}
                    mt={moderateScale(6)}
                    fontSize={moderateScale(15)}
                    color={"#252622"}
                    fontWeight="500">
                    {userDetails.field_4}
                  </Heading>
                </>
              ) : (
                <Heading
                  fontFamily={"openSans"}
                  mt={moderateScale(6)}
                  fontSize={moderateScale(15)}
                  color={"#252622"}
                  fontWeight="500">
                  {dataDetails.receiverPhoneNumber}
                </Heading>
              )}

              <Heading
                fontFamily={"openSans"}
                mt={moderateScale(6)}
                fontSize={moderateScale(15)}
                color={"#252622"}
                fontWeight="500">
                Amount: {dataDetails.currency}{" "}
                {dataDetails.amount === "05" ? "0.5" : dataDetails.amount}
              </Heading>
            </VStack>
          </Center>
        </View>
        <Box h={100}>
          <HStack justifyContent={"center"}>
            {pins.split("").map((item, key) => {
              return (
                <Text
                  key={key}
                  fontFamily={"openSansBold"}
                  fontSize={moderateScale(30)}
                  color={"#F68C1E"}
                  fontWeight="500">
                  {item}
                </Text>
              );
            })}
          </HStack>
        </Box>
        <Box overflow={"hidden"}>
          <View mb={moderateScale(24)}>
            <VirtualKeyboard
              ref={pinRef}
              onChange={(pin) => {
                if (pin.split("").length === 4) {
                  handleBuyAirtime();
                  setPin("");
                  return;
                }
                setPin(pin);
              }}
              keyStyle={{
                backgroundColor: "#f2f6fb",
                margin: 20,
                borderRadius: 100,
                height: 60,
              }}
              keyTextStyle={{
                fontSize: 24,
                color: "#F68C1E",
                fontFamily: "openSansBold",
              }}
              keyboardCustomBackKey={
                <FontAwesome5 name="backspace" size={24} color="black" />
              }
            />
          </View>
        </Box>
      </View>
    </SafeAreaView>
  );
};

export default ConfirmAirtime;
