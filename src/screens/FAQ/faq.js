import { MaterialCommunityIcons } from "@expo/vector-icons";
import { Box, HStack, ScrollView, StatusBar, Text, VStack } from "native-base";
import React, { useState } from "react";
import { SafeAreaView, TouchableOpacity } from "react-native";
import { moderateScale } from "react-native-size-matters";

const FAQ = () => {
  const [expandedItems, setExpandedItems] = useState({});

  const toggleExpanded = (index) => {
    setExpandedItems((prev) => ({
      ...prev,
      [index]: !prev[index],
    }));
  };

  const faqData = [
    {
      question: "How do I recharge my NetOne line?",
      answer: [
        "(a) Dial *133*Enter PIN#",
        "(b) Dial *171#, select option (6) Airtime Recharge > Select option (1) Self > Enter Voucher PIN then Confirm",
        "(c) You may purchase airtime via OneMoney *111#",
      ],
    },
    {
      question:
        "I over-scratched my recharge card, how do I retrieve the voucher PIN?",
      answer: [
        "You can retrieve voucher PIN via our Self-Help portal by dialing *171# - Select Option 6 > Go to Option (3) - Damaged > Enter the serial number of the scratch card, then PIN will be displayed on-screen.",
      ],
    },
    {
      question: "My account is locked; how do I unlock it?",
      answer: [
        "This means you have tried to recharge your account with a wrong or used voucher PIN. Kindly call our Contact Centre on 123 or 121 to get your account unlocked.",
      ],
    },
    {
      question: "How do I recharge another prepaid account?",
      answer: [
        "Simply dial *133*Recharge PIN*Recipients number# or *171#, select option (6) Airtime Recharge > Select option (2) - Other > Enter Voucher PIN then Confirm.",
      ],
    },
    {
      question: "How do I check my airtime balance?",
      answer: [
        "To check your airtime balance dial *171# - Select option (7) - Balance Enquiry > follow on screen instructions.",
      ],
    },
    {
      question: "How do I buy NetOne bundles?",
      answer: [
        "Dial *171# and select the desired category and value option. Note all social media and SMS bundles are found under option (3) - Bundles",
      ],
    },
  ];

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: "white" }}>
      <StatusBar backgroundColor={"white"} barStyle="dark-content" />
      <ScrollView flex={1} bg={"gray.50"} showsVerticalScrollIndicator={false}>
        {/* Header */}
        <Box bg={"white"} px={moderateScale(20)} pt={moderateScale(10)}>
          <VStack space={moderateScale(16)} py={moderateScale(16)}>
            <Text
              fontFamily={"openSansBold"}
              fontSize={moderateScale(28)}
              color={"gray.900"}>
              FAQ
            </Text>
            <Text
              fontFamily={"openSansMedium"}
              fontSize={moderateScale(16)}
              color={"gray.500"}>
              Frequently asked questions
            </Text>
          </VStack>
        </Box>

        {/* FAQ Items */}
        <Box px={moderateScale(20)} py={moderateScale(20)}>
          <VStack space={moderateScale(12)}>
            {faqData.map((item, index) => (
              <TouchableOpacity
                key={index}
                onPress={() => toggleExpanded(index)}>
                <Box
                  bg={"white"}
                  rounded={"2xl"}
                  p={moderateScale(20)}
                  shadow={2}
                  borderWidth={1}
                  borderColor={"gray.100"}>
                  <VStack space={moderateScale(12)}>
                    <HStack
                      justifyContent={"space-between"}
                      alignItems={"center"}>
                      <Text
                        fontFamily={"openSansBold"}
                        fontSize={moderateScale(16)}
                        color={"gray.900"}
                        flex={1}
                        pr={moderateScale(12)}>
                        {item.question}
                      </Text>
                      <MaterialCommunityIcons
                        name={
                          expandedItems[index] ? "chevron-up" : "chevron-down"
                        }
                        size={moderateScale(20)}
                        color="#9CA3AF"
                      />
                    </HStack>

                    {expandedItems[index] && (
                      <VStack space={moderateScale(8)}>
                        {item.answer.map((answerItem, answerIndex) => (
                          <Text
                            key={answerIndex}
                            fontFamily={"openSansMedium"}
                            fontSize={moderateScale(14)}
                            color={"gray.600"}
                            lineHeight={moderateScale(20)}>
                            {answerItem}
                          </Text>
                        ))}
                      </VStack>
                    )}
                  </VStack>
                </Box>
              </TouchableOpacity>
            ))}
          </VStack>
        </Box>
      </ScrollView>
    </SafeAreaView>
  );
};

export default FAQ;
