import { Feather } from "@expo/vector-icons";
import { useNavigation } from "@react-navigation/native";
import {
  Box,
  Button,
  FormControl,
  Heading,
  Input,
  InputGroup,
  InputRightAddon,
  ScrollView,
  Text,
  VStack,
  View,
} from "native-base";
import React, { useEffect, useState } from "react";
import { StyleSheet } from "react-native";
import Toast from "react-native-root-toast";
import { moderateScale, scale, verticalScale } from "react-native-size-matters";
import LoadingModal from "../components/Loading/LoadingModal";
import AuthService from "../services/Auth/Auth";

const SetForgotPassword = (props) => {
  const [oldPin, setOldPin] = useState("");
  const [newPin, setNewPin] = useState("");
  const [showpin, setShowpin] = useState(true);
  const [newPinError, setNewPinError] = useState(false);
  const [confirmPin, setConfirmPin] = useState("");
  const [showConfirmPin, setShowConfirmPin] = useState(true);
  const [confirmPinError, setConfirmPinError] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const [loading, setLoading] = useState(false);
  const [failed, setFailed] = useState(false);
  const [success, setSuccess] = useState(false);
  const [successMessage, setSuccessMessage] = useState("");
  const navigation = useNavigation();
  useEffect(() => {
    const interval = setTimeout(() => {
      setFailed(false);
    }, 5000);

    return () => clearInterval(interval);
  }, [failed]);

  const validate = () => {
    if (!newPin) {
      setNewPinError("This field are required");
      return false;
    }
    if (!confirmPin) {
      setConfirmPinError("This field are required");
      return false;
    }
    if (newPin !== confirmPin) {
      setConfirmPinError("New PIN and Confirm PIN do not match");
      return false;
    }
    return true;
  };

  const handleChangePin = async () => {
    try {
      const res = validate();
      if (!res) {
        return;
      }
      setLoading(true);
      const data = {
        password: newPin,
        token: props.route.params.data,
      };

      const response = await AuthService.forgotPassword(data);
      if (response.data.success) {
        setSuccess(true);
        setSuccessMessage("Failed to change password, Please try again");
        navigation.navigate("Login");
      } else {
        setFailed(true);
        setErrorMessage("Failed to change password, Please try again");
      }
      setNewPin("");
      setConfirmPin("");
      setErrorMessage("");
      setLoading(false);
    } catch (error) {
      setLoading(false);
      setFailed(true);

      if (error.response) {
        if (error.response.status == 400) {
          setErrorMessage("Something went wrong, Please try again");
        } else if (
          error.response.status == 403 ||
          error.response.status == 500 ||
          error.response.status == 504
        ) {
          setErrorMessage("Server Error, Please try again later");
        } else {
          if (error.response.data.message) {
            setErrorMessage(error.response.data.message);
          } else {
            setErrorMessage("Something went wrong, Please try again");
          }
        }
      } else if (error.request) {
        setErrorMessage("Network Error, Please check your internet connection");
      } else {
        setErrorMessage("Something went wrong, Please try again");
      }
    }
  };

  return (
    <ScrollView w="100%" bg={"white"} h={"100%"} px={moderateScale(24)}>
      <Toast
        visible={failed}
        position={30}
        shadow={true}
        animation={true}
        hideOnPress={true}
        backgroundColor={"red"}
        opacity={0.9}>
        {errorMessage}
      </Toast>

      <Toast
        visible={success}
        position={30}
        shadow={true}
        animation={true}
        hideOnPress={true}
        backgroundColor={"green"}
        opacity={0.9}>
        {successMessage}
      </Toast>
      <View pt={moderateScale(24)}>
        <LoadingModal isLoading={loading} />

        <Box safeArea w="100%">
          <Heading
            fontFamily={"openSansSemiBold"}
            fontSize={moderateScale(20)}
            fontWeight="600"
            color="coolGray.800">
            Set New Password
          </Heading>

          <VStack space={moderateScale(16)} mt={moderateScale(48)}>
            <FormControl>
              <InputGroup>
                <Input
                  flex={1}
                  secureTextEntry={showpin}
                  fontFamily={"openSansSemiBold"}
                  onFocus={() => {
                    setNewPinError("");
                  }}
                  focusOutlineColor={"#F68C1E"}
                  borderColor={newPinError ? "red.400" : "gray.300"}
                  placeholder="New Password"
                  fontSize={moderateScale(15)}
                  bgColor={"gray.100"}
                  rounded={"lg"}
                  onChangeText={(text) => setNewPin(text)}
                  h={verticalScale(48)}
                  keyboardType={"default"}
                />
                <InputRightAddon
                  roundedRight={"lg"}
                  w={scale(40)}
                  children={
                    <Feather
                      name={showpin ? "eye-off" : "eye"}
                      size={moderateScale(20)}
                      color="gray"
                      onPress={() => {
                        setShowpin(!showpin);
                      }}
                    />
                  }
                />
              </InputGroup>
              <Text color={"red.400"} fontSize={moderateScale(15)}>
                {newPinError}
              </Text>
            </FormControl>
            <FormControl>
              <InputGroup>
                <Input
                  flex={1}
                  secureTextEntry={showConfirmPin}
                  fontFamily={"openSansSemiBold"}
                  onFocus={() => {
                    setConfirmPinError("");
                  }}
                  focusOutlineColor={"#F68C1E"}
                  borderColor={confirmPinError ? "red.400" : "gray.300"}
                  placeholder="Confirm Password"
                  fontSize={moderateScale(15)}
                  bgColor={"gray.100"}
                  rounded={"lg"}
                  onChangeText={(text) => setConfirmPin(text)}
                  h={verticalScale(48)}
                  keyboardType={"default"}
                />
                <InputRightAddon
                  roundedRight={"lg"}
                  w={scale(40)}
                  children={
                    <Feather
                      name={showConfirmPin ? "eye-off" : "eye"}
                      size={moderateScale(20)}
                      color="gray"
                      onPress={() => {
                        setShowConfirmPin(!showConfirmPin);
                      }}
                    />
                  }
                />
              </InputGroup>
              <Text color={"red.400"} fontSize={moderateScale(15)}>
                {confirmPinError}
              </Text>
            </FormControl>

            <Button
              mt={moderateScale(24)}
              w={"100%"}
              rounded={"lg"}
              onPress={() => handleChangePin()}
              h={verticalScale(48)}
              bg="#F68C1E">
              <Text fontSize={moderateScale(15)} color="white">
                Set Password
              </Text>
            </Button>
          </VStack>
        </Box>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: "center",
    padding: 20,
  },
  label: {
    fontSize: 16,
    marginTop: 20,
  },
  input: {
    width: "100%",
    height: 40,
    borderColor: "gray",
    borderWidth: 1,
    marginTop: 10,
    padding: 10,
  },
  button: {
    marginTop: 20,
    backgroundColor: "blue",
    padding: 10,
  },
  buttonText: {
    color: "white",
    fontSize: 16,
  },
  errorMessage: {
    marginTop: 20,
    color: "red",
    fontSize: 16,
  },
});

export default SetForgotPassword;
